<?php
/**
 * فحص الإعدادات المحفوظة حالياً
 */

require_once __DIR__ . '/config/unified_db_config.php';

$db = getUnifiedDB();
if (!$db) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

echo "<h2>الإعدادات المحفوظة حالياً</h2>";

// جلب جميع الإعدادات
$settings_result = $db->query("SELECT * FROM system_settings ORDER BY setting_key");
if ($settings_result && $settings_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>نوع الإعداد</th><th>المفتاح</th><th>القيمة</th><th>تاريخ التحديث</th></tr>";
    
    $sales_settings = [];
    $purchase_settings = [];
    $company_settings = [];
    
    while ($row = $settings_result->fetch_assoc()) {
        $key = $row['setting_key'];
        $value = $row['setting_value'];
        
        // تصنيف الإعدادات
        if (strpos($key, 'invoice_') === 0 || strpos($key, 'tax_') === 0 || strpos($key, 'currency_') === 0 || 
            strpos($key, 'decimal_') === 0 || strpos($key, 'payment_') === 0 || strpos($key, 'auto_invoice') === 0 ||
            strpos($key, 'require_customer') === 0 || strpos($key, 'allow_negative') === 0) {
            $sales_settings[$key] = $value;
            $type = "إعدادات المبيعات";
            $color = "#e8f5e8";
        } elseif (strpos($key, 'purchase_') === 0 || strpos($key, 'supplier') === 0 || strpos($key, 'stock') === 0 || strpos($key, 'approval') === 0) {
            $purchase_settings[$key] = $value;
            $type = "إعدادات المشتريات";
            $color = "#ffe8e8";
        } elseif (strpos($key, 'company_') === 0 || strpos($key, 'system_') === 0 || strpos($key, 'show_') === 0) {
            $company_settings[$key] = $value;
            $type = "معلومات الشركة";
            $color = "#e8f0ff";
        } else {
            $type = "إعدادات أخرى";
            $color = "#f8f8f8";
        }
        
        echo "<tr style='background: $color;'>";
        echo "<td><strong>$type</strong></td>";
        echo "<td>" . htmlspecialchars($key) . "</td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض ملخص الإعدادات
    echo "<h3>ملخص الإعدادات:</h3>";
    echo "<ul>";
    echo "<li><strong>إعدادات المبيعات:</strong> " . count($sales_settings) . " إعداد</li>";
    echo "<li><strong>إعدادات المشتريات:</strong> " . count($purchase_settings) . " إعداد</li>";
    echo "<li><strong>معلومات الشركة:</strong> " . count($company_settings) . " إعداد</li>";
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>لا توجد إعدادات محفوظة</p>";
}

// دالة لجلب إعداد
function getCurrentSetting($key, $default = '') {
    global $db;
    $stmt = $db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    if (!$stmt) return $default;
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    return $row ? $row['setting_value'] : $default;
}

echo "<h3>الإعدادات الحالية للتطبيق:</h3>";

// إعدادات المبيعات
echo "<h4 style='color: green;'>إعدادات المبيعات:</h4>";
echo "<ul>";
echo "<li>معدل الضريبة الافتراضي: " . getCurrentSetting('default_tax_rate', '15') . "%</li>";
echo "<li>بادئة رقم الفاتورة: " . getCurrentSetting('invoice_prefix', 'INV') . "</li>";
echo "<li>رمز العملة: " . getCurrentSetting('currency_symbol', 'ريال') . "</li>";
echo "<li>عدد الخانات العشرية: " . getCurrentSetting('decimal_places', '2') . "</li>";
echo "<li>طريقة الدفع الافتراضية: " . getCurrentSetting('default_payment_method', 'cash') . "</li>";
echo "<li>ترقيم تلقائي للفواتير: " . (getCurrentSetting('auto_invoice_number', '1') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>إجبار اختيار عميل: " . (getCurrentSetting('require_customer', '0') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>السماح بالمخزون السالب: " . (getCurrentSetting('allow_negative_stock', '0') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "</ul>";

// إعدادات المشتريات
echo "<h4 style='color: red;'>إعدادات المشتريات:</h4>";
echo "<ul>";
echo "<li>بادئة رقم فاتورة الشراء: " . getCurrentSetting('purchase_invoice_prefix', 'PUR') . "</li>";
echo "<li>ترقيم تلقائي لفواتير الشراء: " . (getCurrentSetting('auto_purchase_number', '1') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>إجبار اختيار مورد: " . (getCurrentSetting('require_supplier', '0') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>تحديث المخزون تلقائياً: " . (getCurrentSetting('auto_update_stock', '1') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>يتطلب موافقة على المشتريات: " . (getCurrentSetting('purchase_approval_required', '0') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "</ul>";

// معلومات الشركة
echo "<h4 style='color: blue;'>معلومات الشركة:</h4>";
echo "<ul>";
echo "<li>اسم الشركة: " . getCurrentSetting('company_name', 'نظام المبيعات') . "</li>";
echo "<li>اسم الشركة (إنجليزي): " . getCurrentSetting('company_name_en', 'Sales System') . "</li>";
echo "<li>عنوان الشركة: " . getCurrentSetting('company_address', 'غير محدد') . "</li>";
echo "<li>رقم الهاتف: " . getCurrentSetting('company_phone', 'غير محدد') . "</li>";
echo "<li>البريد الإلكتروني: " . getCurrentSetting('company_email', 'غير محدد') . "</li>";
echo "<li>الموقع الإلكتروني: " . getCurrentSetting('company_website', 'غير محدد') . "</li>";
echo "<li>الرقم الضريبي: " . getCurrentSetting('company_tax_number', 'غير محدد') . "</li>";
echo "<li>رقم السجل التجاري: " . getCurrentSetting('company_commercial_register', 'غير محدد') . "</li>";
echo "<li>إصدار النظام: " . getCurrentSetting('system_version', '2.0') . "</li>";
echo "<li>عرض الشعار في الفواتير: " . (getCurrentSetting('show_logo_on_invoices', '1') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "<li>عرض معلومات الشركة في الفواتير: " . (getCurrentSetting('show_company_info_on_invoices', '1') == '1' ? 'مفعل' : 'معطل') . "</li>";
echo "</ul>";

$logo = getCurrentSetting('company_logo');
if ($logo) {
    echo "<p><strong>الشعار الحالي:</strong> <img src='uploads/$logo' alt='شعار الشركة' style='max-height: 50px;'></p>";
}
?>

<hr>
<p><a href="admin_system.php">العودة لصفحة الإعدادات</a></p>
