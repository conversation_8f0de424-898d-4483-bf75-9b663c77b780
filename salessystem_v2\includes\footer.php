</div> <!-- Close container opened in header -->

<!-- Back to top button -->
<a href="#" class="btn btn-primary back-to-top" role="button">
    <i class="fas fa-arrow-up"></i>
</a>

<!-- Footer -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <!--<div class="row">
            <div class="col-md-4 mb-3 mb-md-0">
                <h5 class="mb-3"><?php echo __('app_name'); ?></h5>
                <p class="text-muted"><?php echo __('footer_description'); ?></p>
            </div>
            <div class="col-md-4 mb-3 mb-md-0">
                <h5 class="mb-3"><?php echo __('quick_links'); ?></h5>
                <ul class="list-unstyled">
                    <li><a href="index.php" class="text-decoration-none text-muted"><i class="fas fa-angle-right me-2"></i><?php echo __('home'); ?></a></li>
                    <li><a href="sales.php" class="text-decoration-none text-muted"><i class="fas fa-angle-right me-2"></i><?php echo __('sales'); ?></a></li>
                    <li><a href="purchases.php" class="text-decoration-none text-muted"><i class="fas fa-angle-right me-2"></i><?php echo __('purchases'); ?></a></li>
                    <li><a href="reports.php" class="text-decoration-none text-muted"><i class="fas fa-angle-right me-2"></i><?php echo __('reports'); ?></a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5 class="mb-3"><?php echo __('contact_us'); ?></h5>
                <ul class="list-unstyled text-muted">
                    <li class="mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    <li class="mb-2"><i class="fas fa-phone me-2"></i> +966 ************</li>
                    <li><i class="fas fa-map-marker-alt me-2"></i> <?php echo __('address'); ?></li>
                </ul>
            </div>
        </div>-->
        <hr class="my-4">
        <div class="row align-items-center">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo __('app_name'); ?>. <?php echo __('all_rights_reserved'); ?></p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <div class="d-inline-flex align-items-center">
                    <a href="admin_login.php" class="text-danger me-4 text-decoration-none">
                        <i class="fas fa-shield-alt me-1"></i>لوحة تحكم المدير
                    </a>
                    <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-white me-3"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript Libraries -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Custom JS -->
<script src="assets/js/script.js"></script>

<script>
// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Back to top button
    var backToTopButton = document.querySelector('.back-to-top');

    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('show');
        } else {
            backToTopButton.classList.remove('show');
        }
    });

    backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Add active class to current nav item
    var currentPage = window.location.pathname.split('/').pop();
    if (currentPage === '') currentPage = 'index.php';

    var navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(function(link) {
        var href = link.getAttribute('href');
        if (href === currentPage) {
            link.classList.add('active');
        }
    });
});
</script>
</body>
</html>
<?php
// Flush the output buffer and send content to browser
if (ob_get_length()) {
    ob_end_flush();
}
?>