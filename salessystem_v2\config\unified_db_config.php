<?php
/**
 * اتصال قاعدة البيانات - أقصر طريقة ممكنة
 */

// ثوابت قاعدة البيانات للتوافق
define('MAIN_DB_HOST', 'localhost');
define('MAIN_DB_USER', 'u193708811_system_main');
define('MAIN_DB_PASS', 'dNz35nd5@');
define('MAIN_DB_NAME', 'u193708811_system_main');

// ثوابت إضافية للتوافق مع النظام القديم
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'sales01');
define('DB_PASSWORD', 'dNz35nd5@');
define('DB_NAME', 'u193708811_system_main');
define('DB_CHARSET', 'utf8mb4');

// متغير الاتصال
$db = null;

// دالة الاتصال المختصرة مع محاولات متعددة وتسجيل الأخطاء
function getDB() {
    global $db;
    if ($db && !$db->connect_error) return $db;

    // قائمة المحاولات المختلفة (مرتبة حسب الأولوية)
    $attempts = [
        ['localhost', 'u193708811_system_main', 'dNz35nd5@', 'u193708811_system_main'],
        ['127.0.0.1', 'u193708811_sales01', 'dNz35nd5@', 'u193708811_system_main'],
        ['localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main'],
        ['127.0.0.1', 'sales01', 'dNz35nd5@', 'u193708811_system_main'],
        ['localhost', 'sales02', 'dNz35nd5@', 'u193708811_system_main'],
        ['127.0.0.1', 'sales02', 'dNz35nd5@', 'u193708811_system_main']
    ];

    $last_error = '';
    foreach ($attempts as $i => $attempt) {
        try {
            $db = @new mysqli($attempt[0], $attempt[1], $attempt[2], $attempt[3]);
            if ($db && !$db->connect_error) {
                $db->set_charset("utf8mb4");
                // تسجيل الاتصال الناجح
                error_log("نجح الاتصال بقاعدة البيانات: " . $attempt[1] . "@" . $attempt[0]);
                return $db;
            } else {
                $last_error = $db ? $db->connect_error : 'فشل إنشاء كائن mysqli';
            }
        } catch (Exception $e) {
            $last_error = $e->getMessage();
        }
    }

    // تسجيل فشل جميع المحاولات
    error_log("فشل في جميع محاولات الاتصال. آخر خطأ: " . $last_error);
    return null;
}

// دوال التوافق
function getUnifiedDB() { return getDB(); }
function getMainDB() { return getDB(); }
function getOperationsDB() { return getDB(); }

// دالة اختبار مختصرة
function testDatabaseConnection() {
    $db = getDB();
    if (!$db) return ['success' => false, 'error' => 'فشل الاتصال'];

    $result = $db->query("SELECT 1");
    return $result ?
        ['success' => true, 'message' => 'نجح الاتصال'] :
        ['success' => false, 'error' => $db->error];
}

// دالة لإنشاء جميع الجداول في قاعدة البيانات الموحدة
function createUnifiedTables() {
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    // جدول المستخدمين
    $users_sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `status` enum('active','inactive','suspended') DEFAULT 'active',
        `reset_token` varchar(255) DEFAULT NULL,
        `reset_token_expires` timestamp NULL DEFAULT NULL,
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_status` (`status`),
        KEY `idx_reset_token` (`reset_token`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المدراء
    $admins_sql = "CREATE TABLE IF NOT EXISTS `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `phone` int(20) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
        `permissions` text DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_role` (`role`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول سجل النشاطات
    $activity_log_sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) DEFAULT NULL,
        `user_type` enum('user','admin') DEFAULT 'user',
        `action` varchar(100) NOT NULL,
        `table_name` varchar(50) DEFAULT NULL,
        `record_id` int(11) DEFAULT NULL,
        `old_data` text DEFAULT NULL,
        `new_data` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_user_type` (`user_type`),
        KEY `idx_action` (`action`),
        KEY `idx_table_name` (`table_name`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول العملاء (بدون بادئة)
    $customers_sql = "CREATE TABLE IF NOT EXISTS `customers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `email` varchar(255) DEFAULT NULL,
        `tax_number` varchar(50) DEFAULT NULL,
        `address` text DEFAULT NULL,
        `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_name` (`name`),
        KEY `idx_email` (`email`),
        KEY `idx_phone` (`phone`),
        KEY `idx_customer_type` (`customer_type`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المنتجات المشتركة (بدون user_id - مشتركة بين جميع المستخدمين)
    $products_sql = "CREATE TABLE IF NOT EXISTS `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
        `category` varchar(100) DEFAULT NULL,
        `stock_quantity` decimal(10,2) DEFAULT 0.00,
        `unit` varchar(50) DEFAULT 'قطعة',
        `barcode` varchar(100) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_by` int(11) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_name` (`name`),
        KEY `idx_category` (`category`),
        KEY `idx_barcode` (`barcode`),
        KEY `idx_active` (`is_active`),
        KEY `idx_created_by` (`created_by`),
        FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المبيعات (بدون بادئة)
    $sales_sql = "CREATE TABLE IF NOT EXISTS `sales` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_method` enum('cash','card','bank_transfer','check','installment','other') DEFAULT 'cash',
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `paid_amount` decimal(10,2) DEFAULT 0.00,
        `remaining_amount` decimal(10,2) DEFAULT 0.00,
        `payment_date` date DEFAULT NULL,
        `payment_reference` varchar(100) DEFAULT NULL,
        `payment_notes` text DEFAULT NULL,
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_method` (`payment_method`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المشتريات (بدون بادئة)
    $purchases_sql = "CREATE TABLE IF NOT EXISTS `purchases` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `supplier_name` varchar(255) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_method` enum('cash','card','bank_transfer','check','installment','other') DEFAULT 'cash',
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `paid_amount` decimal(10,2) DEFAULT 0.00,
        `remaining_amount` decimal(10,2) DEFAULT 0.00,
        `payment_date` date DEFAULT NULL,
        `payment_reference` varchar(100) DEFAULT NULL,
        `payment_notes` text DEFAULT NULL,
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_method` (`payment_method`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المبيعات (بدون بادئة)
    $sale_items_sql = "CREATE TABLE IF NOT EXISTS `sale_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `sale_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_sale_id` (`sale_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المشتريات (بدون بادئة)
    $purchase_items_sql = "CREATE TABLE IF NOT EXISTS `purchase_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `purchase_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_purchase_id` (`purchase_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`purchase_id`) REFERENCES `purchases`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // تنفيذ إنشاء الجداول
    $tables = [
        'users' => $users_sql,
        'admins' => $admins_sql,
        'activity_log' => $activity_log_sql,
        'customers' => $customers_sql,
        'products' => $products_sql,
        'sales' => $sales_sql,
        'purchases' => $purchases_sql,
        'sale_items' => $sale_items_sql,
        'purchase_items' => $purchase_items_sql
    ];
    
    foreach ($tables as $table_name => $sql) {
        if (!$db->query($sql)) {
            error_log("خطأ في إنشاء جدول $table_name: " . $db->error);
            return false;
        }
    }
    
    return true;
}

// دالة للتوافق - إرجاع اسم الجدول بدون بادئة
function getUserTableName($table_name, $username = null) {
    // في النظام الموحد، نستخدم أسماء الجداول مباشرة بدون بادئة
    return $table_name;
}

// دالة للتحقق من كون الجدول مشترك أم خاص بالمستخدم
function isSharedTable($table_name) {
    $shared_tables = ['products']; // المنتجات مشتركة بين جميع المستخدمين
    return in_array($table_name, $shared_tables);
}

// دالة لإدراج المنتجات (مشتركة بدون user_id)
function insertProduct($data) {
    $db = getDB();
    if (!$db) return false;

    // إضافة created_by بدلاً من user_id للمنتجات
    if (isset($_SESSION['user_id'])) {
        $data['created_by'] = $_SESSION['user_id'];
    }

    $columns = array_keys($data);
    $placeholders = array_fill(0, count($data), '?');
    $values = array_values($data);

    $sql = "INSERT INTO `products` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

    $stmt = $db->prepare($sql);
    if (!$stmt) return false;

    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);

    return $stmt->execute() ? $db->insert_id : false;
}

// دالة لتحديث المنتجات (مشتركة بدون فلترة user_id)
function updateProduct($data, $where) {
    $db = getDB();
    if (!$db) return false;

    $set_clauses = [];
    $values = [];
    foreach ($data as $column => $value) {
        $set_clauses[] = "`$column` = ?";
        $values[] = $value;
    }

    $sql = "UPDATE `products` SET " . implode(', ', $set_clauses) . " WHERE $where";

    $stmt = $db->prepare($sql);
    if (!$stmt) return false;

    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);

    return $stmt->execute() ? $stmt->affected_rows : false;
}

// دالة userTableExists() موجودة في includes/database_helper.php

// دالة لإدراج البيانات مع user_id تلقائياً (تتعامل مع الجداول المشتركة)
function insertWithUserId($table, $data, $username = null) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // التحقق من كون الجدول مشترك
    if (isSharedTable($table)) {
        // للجداول المشتركة (مثل المنتجات)، استخدم الدالة المخصصة
        if ($table === 'products') {
            return insertProduct($data);
        }
        // يمكن إضافة جداول مشتركة أخرى هنا
    } else {
        // للجداول الخاصة بالمستخدم، أضف user_id
        $data['user_id'] = $_SESSION['user_id'];
    }

    // بناء استعلام الإدراج
    $db = getDB();
    if (!$db) {
        return false;
    }

    $columns = array_keys($data);
    $placeholders = array_fill(0, count($data), '?');
    $values = array_values($data);

    $sql = "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }

    // تحديد أنواع البيانات
    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);

    $result = $stmt->execute();
    $insert_id = $result ? $db->insert_id : false;
    $stmt->close();

    return $insert_id;
}

// دالة لتحديث البيانات مع فلترة user_id تلقائياً (تتعامل مع الجداول المشتركة)
function updateWithUserId($table, $data, $where, $username = null) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // التحقق من كون الجدول مشترك
    if (isSharedTable($table)) {
        // للجداول المشتركة (مثل المنتجات)، استخدم الدالة المخصصة
        if ($table === 'products') {
            return updateProduct($data, $where);
        }
        // يمكن إضافة جداول مشتركة أخرى هنا
    }

    $db = getDB();
    if (!$db) {
        return false;
    }

    // بناء استعلام التحديث مع فلترة user_id للجداول الخاصة
    $set_clauses = [];
    $values = [];
    foreach ($data as $column => $value) {
        $set_clauses[] = "`$column` = ?";
        $values[] = $value;
    }

    // للجداول الخاصة بالمستخدم، أضف فلترة user_id
    if (!isSharedTable($table)) {
        $where_clause = $where . " AND `user_id` = ?";
        $values[] = $_SESSION['user_id'];
    } else {
        $where_clause = $where;
    }

    $sql = "UPDATE `$table` SET " . implode(', ', $set_clauses) . " WHERE $where_clause";

    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }

    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);

    $result = $stmt->execute();
    $affected_rows = $result ? $stmt->affected_rows : 0;
    $stmt->close();

    return $affected_rows;
}

// تهيئة آمنة (بدون استدعاء تلقائي)
$main_db = $unified_db = null;

// إنشاء الجداول عند الحاجة فقط
function initializeDatabase() {
    global $main_db, $unified_db;
    if (!$main_db) {
        $main_db = $unified_db = getDB();
        if ($main_db) {
            createUnifiedTables();
        }
    }
    return $main_db;
}

// دالة للتوافق مع النظام القديم
function createAppTables() {
    return createUnifiedTables();
}

// إضافة جدول إعدادات النظام
function createSystemSettingsTable() {
    $db = getUnifiedDB();
    if (!$db) return false;

    $sql = "CREATE TABLE IF NOT EXISTS `system_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
        `description` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    return $db->query($sql);
}

// دالة إدراج البيانات الأولية للنظام
function insertInitialSystemData() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // إنشاء جدول الإعدادات أولاً
    createSystemSettingsTable();

    // إدراج إعدادات النظام الأساسية
    $default_settings = [
        ['company_name', 'نظام المبيعات والمشتريات', 'text', 'اسم الشركة'],
        ['company_address', '', 'text', 'عنوان الشركة'],
        ['company_phone', '', 'text', 'هاتف الشركة'],
        ['company_email', '', 'text', 'بريد الشركة الإلكتروني'],
        ['default_currency', 'ر.س', 'text', 'العملة الافتراضية'],
        ['default_tax_rate', '15', 'number', 'نسبة الضريبة الافتراضية'],
        ['auto_print_pos', '1', 'boolean', 'طباعة POS تلقائياً']
    ];

    foreach ($default_settings as $setting) {
        $check_sql = "SELECT COUNT(*) as count FROM system_settings WHERE setting_key = ?";
        $check_stmt = $db->prepare($check_sql);
        if ($check_stmt) {
            $check_stmt->bind_param('s', $setting[0]);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] == 0) {
                $insert_sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_sql);
                if ($insert_stmt) {
                    $insert_stmt->bind_param('ssss', $setting[0], $setting[1], $setting[2], $setting[3]);
                    $insert_stmt->execute();
                }
            }
        }
    }

    return true;
}

// دالة إنشاء المدير الرئيسي
function createSuperAdmin() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // التحقق من وجود مدير رئيسي
    $check_sql = "SELECT COUNT(*) as count FROM admins WHERE role = 'super_admin'";
    $check_result = $db->query($check_sql);

    if ($check_result) {
        $row = $check_result->fetch_assoc();
        if ($row['count'] > 0) {
            return true; // يوجد مدير رئيسي بالفعل
        }
    }

    // بيانات المدير الرئيسي الافتراضية
    $username = 'admin';
    $email = '<EMAIL>';
    $phone = '1234567890';
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $full_name = 'المدير الرئيسي';
    $role = 'super_admin';
    $status = 'active';
    $permissions = 'all';

    $insert_sql = "INSERT INTO admins (username, email, phone, password, full_name, role, permissions, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $db->prepare($insert_sql);
    if ($stmt) {
        $stmt->bind_param('ssssssss', $username, $email, $phone, $password, $full_name, $role, $permissions, $status);

        if ($stmt->execute()) {
            return $db->insert_id;
        }
    }

    return false;
}

// دالة التحقق من وجود المدير الرئيسي وإنشاؤه إذا لم يكن موجوداً
function ensureSuperAdminExists() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // التحقق من وجود مدير رئيسي
    $check_sql = "SELECT id, username, email FROM admins WHERE role = 'super_admin' AND status = 'active' LIMIT 1";
    $check_result = $db->query($check_sql);

    if ($check_result && $check_result->num_rows > 0) {
        // يوجد مدير رئيسي نشط
        $admin = $check_result->fetch_assoc();
        return [
            'exists' => true,
            'admin_id' => $admin['id'],
            'username' => $admin['username'],
            'email' => $admin['email']
        ];
    }

    // لا يوجد مدير رئيسي، إنشاء واحد جديد
    $admin_id = createSuperAdmin();

    if ($admin_id) {
        return [
            'exists' => false,
            'created' => true,
            'admin_id' => $admin_id,
            'username' => 'superadmin',
            'email' => '<EMAIL>',
            'default_password' => 'Admin@123456'
        ];
    }

    return [
        'exists' => false,
        'created' => false,
        'error' => 'فشل في إنشاء المدير الرئيسي'
    ];
}

// تشغيل تلقائي آمن
try {
    $db = getUnifiedDB();
    if ($db) {
        createUnifiedTables();
        insertInitialSystemData();
        ensureSuperAdminExists();
    }
} catch (Exception $e) {
    error_log("خطأ في تهيئة قاعدة البيانات: " . $e->getMessage());
}

?>
