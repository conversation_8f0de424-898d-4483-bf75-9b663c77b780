/**
 * أزرار الفواتير السريعة العائمة
 * يتم تضمينها في جميع صفحات النظام
 */

// متغيرات عامة
let itemCounter = 0;

// فتح السلايد الجانبية للفاتورة السريعة
function openQuickInvoice(type) {
    console.log('openQuickInvoice called with type:', type);

    const sidebar = document.getElementById('quickInvoiceSidebar');
    const overlay = document.getElementById('sidebarOverlay');
    const title = document.getElementById('sidebarTitle');
    const typeInput = document.getElementById('invoiceType');

    if (!sidebar || !overlay || !title || !typeInput) {
        console.error('Required elements not found for quick invoice');
        // إذا لم توجد العناصر، انتقل للصفحة الرئيسية
        window.location.href = `index.php?open_quick_invoice=${type}`;
        return;
    }

    // تحديد نوع الفاتورة
    typeInput.value = type;

    if (type === 'sale') {
        title.innerHTML = '<i class="fas fa-file-invoice-dollar me-2"></i>فاتورة مبيعات سريعة';

        // تحديث تسميات العميل
        const customerLabel = document.getElementById('quickCustomerLabel');
        if (customerLabel) {
            customerLabel.textContent = 'العميل';
        }

        // تحديث نافذة إضافة عميل جديد
        updateQuickAddCustomerModal('customer');

    } else {
        title.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>فاتورة مشتريات سريعة';

        // تحديث تسميات المورد
        const customerLabel = document.getElementById('quickCustomerLabel');
        if (customerLabel) {
            customerLabel.textContent = 'المورد';
        }

        // تحديث نافذة إضافة مورد جديد
        updateQuickAddCustomerModal('supplier');
    }

    // تحديث قائمة العملاء/الموردين (إذا كان العنصر موجود)
    try {
        if (document.getElementById('quickCustomer')) {
            updateCustomersList(type);
        }
    } catch (error) {
        console.log('Error updating customers list:', error);
    }

    // إظهار السلايد
    sidebar.classList.add('active');
    overlay.classList.add('active');

    // منع التمرير في الخلفية
    document.body.style.overflow = 'hidden';

    console.log('Quick invoice opened successfully');

    // إضافة أول صف للأصناف
    setTimeout(() => {
        if (typeof addQuickItemRow === 'function') {
            addQuickItemRow();
        }
        if (typeof updateQuickInvoiceSummary === 'function') {
            updateQuickInvoiceSummary();
        }
    }, 100);
}

// دالة تحديث نافذة إضافة عميل/مورد جديد
function updateQuickAddCustomerModal(customerType) {
    const modalTitle = document.getElementById('quickAddCustomerModalLabel');
    const nameLabel = document.getElementById('quickCustomerNameLabel');
    const customerTypeInput = document.getElementById('quick_customer_type');

    if (customerType === 'supplier') {
        if (modalTitle) modalTitle.textContent = 'إضافة مورد جديد';
        if (nameLabel) nameLabel.textContent = 'اسم المورد *';
        if (customerTypeInput) customerTypeInput.value = 'supplier';
    } else {
        if (modalTitle) modalTitle.textContent = 'إضافة عميل جديد';
        if (nameLabel) nameLabel.textContent = 'اسم العميل *';
        if (customerTypeInput) customerTypeInput.value = 'customer';
    }

    console.log('Updated add customer modal for type:', customerType);
}

// دالة تحديث قائمة العملاء/الموردين حسب نوع الفاتورة
function updateCustomersList(invoiceType) {
    const customerSelect = document.getElementById('quickCustomer');

    // التحقق من وجود العنصر
    if (!customerSelect) {
        console.log('quickCustomer element not found, skipping update');
        return;
    }

    const customerType = invoiceType === 'sale' ? 'customer' : 'supplier';
    console.log('Updating customers list for type:', customerType);

    // مسح الخيارات الحالية (عدا الخيارات الثابتة)
    while (customerSelect.children.length > 2) {
        customerSelect.removeChild(customerSelect.lastChild);
    }

    // جلب العملاء/الموردين من الخادم
    fetch(`get_customers_by_type.php?type=${customerType}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Customers data received:', data);
            if (data.success && data.customers) {
                data.customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    if (customer.phone) {
                        option.textContent += ` - ${customer.phone}`;
                    }
                    customerSelect.appendChild(option);
                });
                console.log(`Added ${data.customers.length} ${customerType}s to list`);
            } else {
                console.log('No customers found or error in response');
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
            // لا نوقف تشغيل باقي الوظائف بسبب هذا الخطأ
        });
}

// إغلاق السلايد الجانبية
function closeQuickInvoice() {
    const sidebar = document.getElementById('quickInvoiceSidebar');
    const overlay = document.getElementById('sidebarOverlay');

    if (sidebar && overlay) {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');

        // السماح بالتمرير مرة أخرى
        document.body.style.overflow = 'auto';

        // إعادة تعيين النموذج
        const form = document.getElementById('quickInvoiceForm');
        if (form) {
            form.reset();
        }

        // إعادة تعيين الأصناف
        const itemsBody = document.getElementById('quickItemsBody');
        if (itemsBody) {
            itemsBody.innerHTML = '';
        }

        itemCounter = 0;
        if (typeof updateQuickInvoiceSummary === 'function') {
            updateQuickInvoiceSummary();
        }
    }
}

// إنشاء HTML للأزرار العائمة
function createFloatingButtons() {
    const buttonsHTML = `
        <div class="floating-buttons">
            <button class="floating-btn purchase-btn" onclick="openQuickInvoice('purchase')" title="فاتورة مشتريات سريعة">
                <i class="fas fa-shopping-cart"></i>
                <span class="btn-label">مشتريات</span>
            </button>
            <button class="floating-btn sale-btn" onclick="openQuickInvoice('sale')" title="فاتورة مبيعات سريعة">
                <i class="fas fa-file-invoice-dollar"></i>
                <span class="btn-label">مبيعات</span>
            </button>
        </div>
    `;
    
    // إضافة الأزرار لنهاية الصفحة
    document.body.insertAdjacentHTML('beforeend', buttonsHTML);
}

// إنشاء overlay للسلايد الجانبية
function createSidebarOverlay() {
    const overlayHTML = `
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeQuickInvoice()"></div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', overlayHTML);
}

// فحص وجود الأزرار وإنشاؤها إذا لم تكن موجودة
function ensureFloatingButtons() {
    if (!document.querySelector('.floating-buttons')) {
        createFloatingButtons();
    }
    
    if (!document.getElementById('sidebarOverlay')) {
        createSidebarOverlay();
    }
}

// تهيئة الأزرار عند تحميل الصفحة
function initFloatingButtons() {
    // التحقق من وجود الأزرار
    ensureFloatingButtons();
    
    // إضافة مستمع للضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeQuickInvoice();
        }
    });
    
    // التحقق من معاملات URL لفتح فاتورة سريعة
    const urlParams = new URLSearchParams(window.location.search);
    const openQuickInvoiceType = urlParams.get('open_quick_invoice');
    
    if (openQuickInvoiceType && (openQuickInvoiceType === 'sale' || openQuickInvoiceType === 'purchase')) {
        // تأخير قصير للتأكد من تحميل الصفحة
        setTimeout(() => {
            openQuickInvoice(openQuickInvoiceType);
            
            // إزالة المعامل من URL
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('open_quick_invoice');
            window.history.replaceState({}, document.title, newUrl.toString());
        }, 500);
    }
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initFloatingButtons);

// تشغيل التهيئة إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initFloatingButtons);
} else {
    initFloatingButtons();
}

// إضافة CSS للـ overlay إذا لم يكن موجوداً
function addOverlayStyles() {
    if (!document.getElementById('floating-overlay-styles')) {
        const style = document.createElement('style');
        style.id = 'floating-overlay-styles';
        style.textContent = `
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9998;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        `;
        document.head.appendChild(style);
    }
}

// إضافة الأنماط عند التحميل
addOverlayStyles();
