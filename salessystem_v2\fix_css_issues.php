<?php
/**
 * سكريپت إصلاح مشاكل CSS والتصميم في جميع صفحات المدير
 */

// قائمة جميع صفحات المدير
$admin_pages = [
    'admin_dashboard.php',
    'admin_users.php',
    'admin_activity.php',
    'admin_reports.php', 
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixes_applied = [];
$errors = [];

echo "<h2>🎨 إصلاح مشاكل CSS والتصميم في صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>🎨 معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إصلاح مشاكل التخطيط الأساسية
    $layout_fixes = [
        // إصلاح container-fluid
        '/class="col-lg-9[^"]*"[^>]*style="[^"]*"/' => 'class="admin-content"',
        
        // إصلاح الشريط الجانبي
        '/class="col-lg-3[^"]*sidebar[^"]*"/' => 'class="modern-sidebar d-none d-lg-block"',
        
        // إصلاح البطاقات القديمة
        '/class="card[^"]*border-left[^"]*"/' => 'class="stats-card"',
        '/class="card[^"]*shadow[^"]*"/' => 'class="modern-card"',
        '/class="card-header[^"]*"/' => 'class="modern-card-header"',
        '/class="card-body[^"]*"/' => 'class="modern-card-body"',
        
        // إصلاح الأزرار القديمة
        '/class="btn btn-primary[^"]*"/' => 'class="modern-btn modern-btn-primary"',
        '/class="btn btn-secondary[^"]*"/' => 'class="modern-btn modern-btn-secondary"',
        '/class="btn btn-success[^"]*"/' => 'class="modern-btn modern-btn-success"',
        '/class="btn btn-outline[^"]*"/' => 'class="modern-btn modern-btn-outline"',
        
        // إصلاح الجداول
        '/class="table[^"]*table-bordered[^"]*"/' => 'class="modern-table table"',
        
        // إصلاح النماذج
        '/class="form-control[^"]*"/' => 'class="modern-form-control"',
        '/class="form-label[^"]*"/' => 'class="modern-form-label"',
        '/class="form-select[^"]*"/' => 'class="modern-form-control"',
    ];
    
    foreach ($layout_fixes as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
            echo "✅ تم إصلاح تخطيط<br>";
        }
    }
    
    // 2. إصلاح هيكل الصفحة
    if (strpos($content, '<div class="admin-layout">') === false && 
        strpos($content, '<div class="container-fluid">') !== false) {
        
        $content = str_replace(
            '<div class="container-fluid">',
            '<div class="admin-layout">',
            $content
        );
        $changes_made = true;
        echo "✅ تم إصلاح هيكل الصفحة<br>";
    }
    
    // 3. إصلاح الشريط الجانبي المفقود
    if (strpos($content, 'modern-sidebar') === false && 
        strpos($content, 'admin-content') !== false) {
        
        $sidebar_html = generateSidebar($page);
        $content = str_replace(
            '<main class="admin-content"',
            $sidebar_html . "\n        <main class=\"admin-content\"",
            $content
        );
        $changes_made = true;
        echo "✅ تم إضافة الشريط الجانبي<br>";
    }
    
    // 4. إصلاح رأس الصفحة
    if (strpos($content, 'gradient-text') === false && 
        strpos($content, '<h1') !== false) {
        
        $page_info = getPageInfo($page);
        $header_pattern = '/<h1[^>]*>([^<]+)<\/h1>/';
        if (preg_match($header_pattern, $content, $matches)) {
            $new_header = generateModernHeader($page_info['title'], $page_info['icon'], $page_info['description']);
            $content = preg_replace($header_pattern, $new_header, $content);
            $changes_made = true;
            echo "✅ تم تحديث رأس الصفحة<br>";
        }
    }
    
    // 5. إصلاح مشاكل CSS المضمنة
    $css_fixes = [
        // إزالة الأنماط المضمنة القديمة
        '/style="[^"]*margin-top:[^"]*"/' => '',
        '/style="[^"]*padding:[^"]*"/' => '',
        '/style="[^"]*background:[^"]*"/' => '',
        
        // إصلاح الفئات المفقودة
        '/<div class="">/' => '<div>',
        '/<span class="">/' => '<span>',
    ];
    
    foreach ($css_fixes as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
        }
    }
    
    // 6. إضافة CSS مخصص إذا لم يكن موجوداً
    if (strpos($content, '<style>') === false && $changes_made) {
        $custom_css = generateCustomCSS();
        $content = str_replace('</head>', $custom_css . "\n</head>", $content);
        echo "✅ تم إضافة CSS مخصص<br>";
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.css_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixes_applied[] = $page;
            echo "✅ تم حفظ التحديثات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ التحديثات<br>";
        }
    } else {
        echo "ℹ️ لا توجد مشاكل CSS<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>📋 ملخص إصلاح CSS:</h3>";

if (!empty($fixes_applied)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُصلحة بنجاح:</h4>";
    foreach ($fixes_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<br><a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>العودة للوحة التحكم</a>";

/**
 * دوال مساعدة
 */
function getPageInfo($page) {
    $pages_info = [
        'admin_dashboard.php' => ['title' => 'لوحة تحكم المدير', 'icon' => 'fa-crown', 'description' => 'مرحباً بك في نظام إدارة المبيعات المتطور'],
        'admin_users.php' => ['title' => 'إدارة المستخدمين', 'icon' => 'fa-users', 'description' => 'إدارة وتنظيم حسابات المستخدمين في النظام'],
        'admin_activity.php' => ['title' => 'سجل العمليات والأنشطة', 'icon' => 'fa-history', 'description' => 'مراقبة وتتبع جميع أنشطة المستخدمين والمديرين'],
        'admin_reports.php' => ['title' => 'التقارير الشاملة', 'icon' => 'fa-chart-bar', 'description' => 'عرض وتحليل التقارير الشاملة للنظام'],
        'admin_financial.php' => ['title' => 'التقارير المالية', 'icon' => 'fa-file-invoice-dollar', 'description' => 'مراجعة التقارير المالية والإحصائيات'],
        'admin_error_logs.php' => ['title' => 'سجل الأخطاء', 'icon' => 'fa-exclamation-triangle', 'description' => 'مراقبة وتتبع أخطاء النظام'],
        'admin_system.php' => ['title' => 'إعدادات النظام', 'icon' => 'fa-cogs', 'description' => 'إدارة وتكوين إعدادات النظام'],
        'admin_manage_admins.php' => ['title' => 'إدارة المديرين', 'icon' => 'fa-user-shield', 'description' => 'إدارة حسابات المديرين والصلاحيات']
    ];
    
    return $pages_info[$page] ?? ['title' => 'صفحة المدير', 'icon' => 'fa-cog', 'description' => 'إدارة النظام'];
}

function generateSidebar($current_page) {
    return '        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link' . ($current_page === 'admin_dashboard.php' ? ' active' : '') . '" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link' . ($current_page === 'admin_users.php' ? ' active' : '') . '" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link' . ($current_page === 'admin_activity.php' ? ' active' : '') . '" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link' . ($current_page === 'admin_reports.php' ? ' active' : '') . '" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link' . ($current_page === 'admin_financial.php' ? ' active' : '') . '" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link' . ($current_page === 'admin_error_logs.php' ? ' active' : '') . '" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link' . ($current_page === 'admin_system.php' ? ' active' : '') . '" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link' . ($current_page === 'admin_manage_admins.php' ? ' active' : '') . '" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>';
}

function generateModernHeader($title, $icon, $description) {
    return '<div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas ' . $icon . ' me-3"></i>
                        ' . $title . '
                    </h1>
                    <p class="text-muted mb-0">' . $description . '</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>';
}

function generateCustomCSS() {
    return '<style>
/* إصلاحات CSS مخصصة */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

.modern-sidebar {
    width: 280px;
    background: var(--admin-sidebar-bg);
    border-right: 1px solid var(--admin-border-color);
}

.admin-content {
    flex: 1;
    padding: 2rem;
    background: var(--admin-bg);
}

.gradient-text {
    background: var(--admin-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modern-card {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border-color);
    border-radius: 12px;
    box-shadow: var(--admin-shadow-sm);
}

.modern-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.modern-btn-primary {
    background: var(--admin-gradient-primary);
    color: white;
}

.modern-table {
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table th {
    background: var(--admin-table-header-bg);
    color: white;
    font-weight: 600;
    padding: 1rem 0.75rem;
}
</style>';
}
?>
