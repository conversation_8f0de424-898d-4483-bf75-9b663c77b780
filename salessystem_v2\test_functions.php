<?php
/**
 * اختبار الدوال للتأكد من عدم وجود تضارب
 */

echo "<h1>اختبار الدوال</h1>";

// اختبار تحميل الملفات
echo "<h2>1. اختبار تحميل الملفات</h2>";

try {
    require_once __DIR__ . '/config/init.php';
    echo "<p style='color: green;'>✅ تم تحميل ملف init.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل ملف init.php: " . $e->getMessage() . "</p>";
    exit;
}

try {
    require_once __DIR__ . '/includes/functions.php';
    echo "<p style='color: green;'>✅ تم تحميل ملف functions.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل ملف functions.php: " . $e->getMessage() . "</p>";
    exit;
}

// اختبار الدوال المطلوبة
echo "<h2>2. اختبار وجود الدوال</h2>";

$required_functions = [
    'isAdminLoggedIn',
    'hasAdminPermission',
    'getCurrentAdmin',
    'getSystemSettings',
    'updateSystemSetting',
    'getSystemSetting',
    'getUnifiedDB',
    'createAppTables',
    'generateInvoiceNumber',
    'displayMessages'
];

foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ دالة $func موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة $func غير موجودة</p>";
    }
}

// اختبار الاتصال بقاعدة البيانات
echo "<h2>3. اختبار قاعدة البيانات</h2>";

try {
    $db = getUnifiedDB();
    if ($db) {
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار دوال إعدادات النظام
echo "<h2>4. اختبار دوال إعدادات النظام</h2>";

try {
    if (function_exists('getSystemSettings')) {
        $settings = getSystemSettings();
        echo "<p style='color: green;'>✅ دالة getSystemSettings تعمل - عدد الإعدادات: " . count($settings) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة getSystemSettings غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة getSystemSettings: " . $e->getMessage() . "</p>";
}

try {
    if (function_exists('getSystemSetting')) {
        $company_name = getSystemSetting('company_name', 'اسم افتراضي');
        echo "<p style='color: green;'>✅ دالة getSystemSetting تعمل - اسم الشركة: $company_name</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة getSystemSetting غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة getSystemSetting: " . $e->getMessage() . "</p>";
}

// اختبار دوال المديرين
echo "<h2>5. اختبار دوال المديرين</h2>";

try {
    if (function_exists('isAdminLoggedIn')) {
        $is_logged = isAdminLoggedIn();
        $status = $is_logged ? 'مسجل دخول' : 'غير مسجل دخول';
        echo "<p style='color: green;'>✅ دالة isAdminLoggedIn تعمل - الحالة: $status</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة isAdminLoggedIn غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة isAdminLoggedIn: " . $e->getMessage() . "</p>";
}

try {
    if (function_exists('getCurrentAdmin')) {
        $admin = getCurrentAdmin();
        if ($admin) {
            echo "<p style='color: green;'>✅ دالة getCurrentAdmin تعمل - المدير: {$admin['username']}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ دالة getCurrentAdmin تعمل لكن لا يوجد مدير مسجل دخول</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ دالة getCurrentAdmin غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة getCurrentAdmin: " . $e->getMessage() . "</p>";
}

// اختبار دوال أخرى
echo "<h2>6. اختبار دوال أخرى</h2>";

try {
    if (function_exists('generateInvoiceNumber')) {
        $invoice_number = generateInvoiceNumber('TEST');
        echo "<p style='color: green;'>✅ دالة generateInvoiceNumber تعمل - رقم تجريبي: $invoice_number</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة generateInvoiceNumber غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة generateInvoiceNumber: " . $e->getMessage() . "</p>";
}

// عرض معلومات PHP
echo "<h2>7. معلومات النظام</h2>";
echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>الذاكرة المستخدمة:</strong> " . memory_get_usage(true) / 1024 / 1024 . " MB</p>";
echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";

// الروابط المفيدة
echo "<h2>8. الروابط المفيدة</h2>";
echo "<ul>";
echo "<li><a href='admin_login.php'>تسجيل دخول المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php'>إدارة المديرين</a></li>";
echo "<li><a href='fix_admin_pages.php'>إصلاح صفحات المدير</a></li>";
echo "<li><a href='test_system.php'>اختبار النظام الشامل</a></li>";
echo "</ul>";

echo "<h2>✅ انتهى الاختبار</h2>";
echo "<p style='color: green; font-weight: bold;'>تم اختبار جميع الدوال بنجاح!</p>";

?>
