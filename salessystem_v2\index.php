<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';
redirectIfNotLoggedIn();

// الحصول على اتصال قاعدة البيانات الموحدة
$db = getUnifiedDB();
if (!$db) {
    die('<div class="alert alert-danger">خطأ في الاتصال بقاعدة البيانات. يرجى التواصل مع المطور.</div>');
}

// استعلام المبيعات اليومية (النظام الموحد)
$today = date('Y-m-d');
$today_sales = 0;

try {
    $sales_stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date = ? AND user_id = ?");
    if ($sales_stmt) {
        $sales_stmt->bind_param("si", $today, $_SESSION['user_id']);
        $sales_stmt->execute();
        $sales_stmt->bind_result($today_sales);
        $sales_stmt->fetch();
        $sales_stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المبيعات: " . $e->getMessage());
}

// استعلام المشتريات اليومية (النظام الموحد)
$today_purchases = 0;
try {
    $purchases_stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date = ? AND user_id = ?");
    if ($purchases_stmt) {
        $purchases_stmt->bind_param("si", $today, $_SESSION['user_id']);
        $purchases_stmt->execute();
        $purchases_stmt->bind_result($today_purchases);
        $purchases_stmt->fetch();
        $purchases_stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المشتريات: " . $e->getMessage());
}

// استعلام عدد العملاء (النظام الموحد)
$customers_count = 0;
try {
    $customers_stmt = $db->prepare("SELECT COUNT(id) FROM customers WHERE user_id = ?");
    if ($customers_stmt) {
        $customers_stmt->bind_param("i", $_SESSION['user_id']);
        $customers_stmt->execute();
        $customers_stmt->bind_result($customers_count);
        $customers_stmt->fetch();
        $customers_stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام العملاء: " . $e->getMessage());
}

// استعلام إجمالي المبيعات والمشتريات للشهر الحالي
$current_month = date('Y-m');
$month_start = $current_month . '-01';
$month_end = date('Y-m-t');

// إجمالي المبيعات الشهرية (النظام الموحد)
$monthly_sales = 0;
try {
    $stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date BETWEEN ? AND ? AND user_id = ?");
    if ($stmt) {
        $stmt->bind_param("ssi", $month_start, $month_end, $_SESSION['user_id']);
        $stmt->execute();
        $stmt->bind_result($monthly_sales);
        $stmt->fetch();
        $stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المبيعات الشهرية: " . $e->getMessage());
}

// إجمالي المشتريات الشهرية (النظام الموحد)
$monthly_purchases = 0;
try {
    $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date BETWEEN ? AND ? AND user_id = ?");
    if ($stmt) {
        $stmt->bind_param("ssi", $month_start, $month_end, $_SESSION['user_id']);
        $stmt->execute();
        $stmt->bind_result($monthly_purchases);
        $stmt->fetch();
        $stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المشتريات الشهرية: " . $e->getMessage());
}

// حساب الربح الشهري
$monthly_profit = ($monthly_sales ?? 0) - ($monthly_purchases ?? 0);

// استعلام عدد الفواتير (النظام الموحد)
$sales_count = 0;
$purchases_count = 0;

try {
    $stmt = $db->prepare("SELECT COUNT(id) FROM sales WHERE user_id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $stmt->bind_result($sales_count);
        $stmt->fetch();
        $stmt->close();
    }

    $stmt = $db->prepare("SELECT COUNT(id) FROM purchases WHERE user_id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $stmt->bind_result($purchases_count);
        $stmt->fetch();
        $stmt->close();
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام عدد الفواتير: " . $e->getMessage());
}



require_once __DIR__.'/includes/header.php';
displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<!-- Welcome Banner -->
<div class="card mb-4 dashboard-card bg-primary text-white">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1"><?php echo __('welcome'); ?>, <?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?>!</h2>
            </div>
            <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <span class="badge bg-light text-primary"><?php echo date('d M Y'); ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <!-- Today's Sales -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('today_sales'); ?></h6>
                    <div class="icon-box bg-primary-light rounded-circle p-2">
                        <i class="fas fa-shopping-bag text-primary"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($today_sales ?? 0, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo min(100, (($today_sales ?? 0) / 1000) * 100); ?>%"></div>
                </div>
                <i class="fas fa-chart-line bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Today's Purchases -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('today_purchases'); ?></h6>
                    <div class="icon-box bg-danger-light rounded-circle p-2">
                        <i class="fas fa-shopping-cart text-danger"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($today_purchases ?? 0, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo min(100, (($today_purchases ?? 0) / 1000) * 100); ?>%"></div>
                </div>
                <i class="fas fa-cart-arrow-down bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Monthly Profit -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('monthly_profit'); ?></h6>
                    <div class="icon-box bg-success-light rounded-circle p-2">
                        <i class="fas fa-dollar-sign text-success"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($monthly_profit, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $monthly_profit > 0 ? min(100, ($monthly_profit / 5000) * 100) : 0; ?>%"></div>
                </div>
                <i class="fas fa-chart-bar bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Customers Count -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('customers'); ?></h6>
                    <div class="icon-box bg-info-light rounded-circle p-2">
                        <i class="fas fa-users text-info"></i>
                    </div>
                </div>
                <h3><?php echo number_format($customers_count); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo min(100, ($customers_count / 50) * 100); ?>%"></div>
                </div>
                <i class="fas fa-user-friends bg-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Tax Summary -->
<div class="row mb-4">
    <!-- Quick Actions Section -->
    <div class="col-md-6">
        <div class="card dashboard-card mb-3">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-bolt"></i> <?php echo __('quick_actions'); ?>
            </div>
            <div class="card-body p-0">
                <div class="row m-0">
                    <div class="col-6 p-0">
                        <a href="add_sale.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-file-invoice" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('add_sale'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="add_purchase.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-shopping-cart" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('add_purchase'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="products.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-boxes" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('products'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="customers.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-users" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('customers'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="reports.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-chart-bar" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('reports'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="tax_calculator.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-calculator" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('tax_calculator'); ?></div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Summary Section -->
    <div class="col-md-6">
        <div class="card dashboard-card mb-3">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-pie"></i> <?php echo __('tax_summary'); ?>
            </div>
            <div class="card-body p-0">
                <div class="row m-0">
                    <!-- Sales Tax -->
                    <div class="col-6 p-0">
                        <div class="bg-light text-dark p-3 h-100">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('sales_tax'); ?></h6>
                                <i class="fas fa-file-invoice text-success"></i>
                            </div>
                            <?php
                            try {
                                $stmt = $db->prepare("SELECT SUM(tax_amount) FROM sales WHERE user_id = ?");
                                if ($stmt) {
                                    $stmt->bind_param("i", $_SESSION['user_id']);
                                    $stmt->execute();
                                    $stmt->bind_result($total_sales_tax);
                                    $stmt->fetch();
                                    $stmt->close();

                                    // استعلام إجمالي المبيعات
                                    $stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE user_id = ?");
                                    $stmt->bind_param("i", $_SESSION['user_id']);
                                    $stmt->execute();
                                    $stmt->bind_result($total_sales);
                                    $stmt->fetch();
                                    $stmt->close();

                                    echo '<h4 class="text-success">' . __('currency') . ' ' . number_format($total_sales_tax ?? 0, 2) . '</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: ' . min(100, (($total_sales_tax ?? 0) / 5000) * 100) . '%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_sales') . ': ' . number_format($total_sales ?? 0, 2) . ' ' . __('currency') . '</p>';
                                } else {
                                    echo '<h4 class="text-success">' . __('currency') . ' 0.00</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_sales') . ': 0.00 ' . __('currency') . '</p>';
                                }
                            } catch (Exception $e) {
                                echo '<h4 class="text-success">' . __('currency') . ' 0.00</h4>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">' . __('total_sales') . ': 0.00 ' . __('currency') . '</p>';
                                error_log("Exception in sales tax query: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Purchases Tax -->
                    <div class="col-6 p-0">
                        <div class="bg-light text-dark p-3 h-100">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('purchases_tax'); ?></h6>
                                <i class="fas fa-shopping-cart text-danger"></i>
                            </div>
                            <?php
                            try {
                                $stmt = $db->prepare("SELECT SUM(tax_amount) FROM purchases WHERE user_id = ?");
                                if ($stmt) {
                                    $stmt->bind_param("i", $_SESSION['user_id']);
                                    $stmt->execute();
                                    $stmt->bind_result($total_purchases_tax);
                                    $stmt->fetch();
                                    $stmt->close();

                                    // استعلام إجمالي المشتريات
                                    $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE user_id = ?");
                                    $stmt->bind_param("i", $_SESSION['user_id']);
                                    $stmt->execute();
                                    $stmt->bind_result($total_purchases);
                                    $stmt->fetch();
                                    $stmt->close();

                                    echo '<h4 class="text-danger">' . __('currency') . ' ' . number_format($total_purchases_tax ?? 0, 2) . '</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: ' . min(100, (($total_purchases_tax ?? 0) / 5000) * 100) . '%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_purchases') . ': ' . number_format($total_purchases ?? 0, 2) . ' ' . __('currency') . '</p>';
                                } else {
                                    echo '<h4 class="text-danger">' . __('currency') . ' 0.00</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_purchases') . ': 0.00 ' . __('currency') . '</p>';
                                }
                            } catch (Exception $e) {
                                echo '<h4 class="text-danger">' . __('currency') . ' 0.00</h4>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">' . __('total_purchases') . ': 0.00 ' . __('currency') . '</p>';
                                error_log("Exception in purchases tax query: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Tax Due -->
                    <div class="col-12 p-0">
                        <div class="bg-primary text-white p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('tax_due'); ?></h6>
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <?php
                            try {
                                $sales_tax = $total_sales_tax ?? 0;
                                $purchases_tax = $total_purchases_tax ?? 0;
                                $tax_due = $sales_tax - $purchases_tax;
                                $tax_due_class = $tax_due >= 0 ? 'text-white' : 'text-warning';

                                echo '<h3 class="' . $tax_due_class . '">' . __('currency') . ' ' . number_format($tax_due, 2) . '</h3>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-white" role="progressbar" style="width: ' . min(100, (abs($tax_due) / 5000) * 100) . '%"></div>
                                </div>';
                                echo '<p class="small mt-2">
                                    <i class="fas fa-info-circle"></i> ' . __('due_to_tax_authority') . '
                                </p>';
                            } catch (Exception $e) {
                                echo '<h3 class="text-white">' . __('currency') . ' 0.00</h3>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-white" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">
                                    <i class="fas fa-info-circle"></i> ' . __('due_to_tax_authority') . '
                                </p>';
                                error_log("Exception in tax due calculation: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mt-4">
    <!-- Recent Sales -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    <?php echo __('recent_sales'); ?>
                </div>
                <div class="badge bg-success"><?php echo $sales_count; ?> <?php echo __('total'); ?></div>
            </div>
            <div class="card-body">
                <div class="search-box mb-3">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-input" data-table="recent-sales-table" placeholder="<?php echo __('search'); ?>...">
                </div>
                <div class="table-responsive">
                    <table class="table table-hover sortable" id="recent-sales-table">
                        <thead>
                            <tr>
                                <th class="sortable"><?php echo __('invoice_number'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('date'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('total'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $query = "SELECT id, invoice_number, date, total_amount FROM sales WHERE user_id = {$_SESSION['user_id']} ORDER BY id DESC LIMIT 5";
                                $result = $db->query($query);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['invoice_number']); ?></td>
                                        <td><?php echo htmlspecialchars($row['date']); ?></td>
                                        <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="viewInvoice(<?php echo $row['id']; ?>, 'sale')" data-bs-toggle="tooltip" title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("Error displaying sales: " . $e->getMessage());
                                echo '<tr><td colspan="4" class="text-center">' . __('error') . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <a href="sales.php" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i> <?php echo __('show_all'); ?>
                    </a>
                    <a href="add_sale.php" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> <?php echo __('add_sale'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Purchases -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-shopping-cart me-2"></i>
                    <?php echo __('recent_purchases'); ?>
                </div>
                <div class="badge bg-danger"><?php echo $purchases_count; ?> <?php echo __('total'); ?></div>
            </div>
            <div class="card-body">
                <div class="search-box mb-3">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-input" data-table="recent-purchases-table" placeholder="<?php echo __('search'); ?>...">
                </div>
                <div class="table-responsive">
                    <table class="table table-hover sortable" id="recent-purchases-table">
                        <thead>
                            <tr>
                                <th class="sortable"><?php echo __('invoice_number'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('date'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('total'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $query = "SELECT id, invoice_number, date, total_amount FROM purchases WHERE user_id = {$_SESSION['user_id']} ORDER BY id DESC LIMIT 5";
                                $result = $db->query($query);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['invoice_number']); ?></td>
                                        <td><?php echo htmlspecialchars($row['date']); ?></td>
                                        <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="viewInvoice(<?php echo $row['id']; ?>, 'purchase')" data-bs-toggle="tooltip" title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("Error displaying purchases: " . $e->getMessage());
                                echo '<tr><td colspan="4" class="text-center">' . __('error') . ': ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <a href="purchases.php" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i> <?php echo __('show_all'); ?>
                    </a>
                    <a href="add_purchase.php" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> <?php echo __('add_purchase'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار عائمة للفواتير السريعة -->
<div class="floating-buttons">
    <button class="floating-btn purchase-btn" id="purchaseBtn" onclick="openQuickInvoice('purchase')" title="فاتورة مشتريات سريعة" style="display: none;">
        <i class="fas fa-shopping-cart"></i>
    </button>
    <button class="floating-btn sale-btn" id="saleBtn" onclick="openQuickInvoice('sale')" title="فاتورة مبيعات سريعة" style="display: none;">
        <i class="fas fa-file-invoice-dollar"></i>
    </button>
    <button class="floating-btn main-btn" onclick="toggleFloatingMenu()" title="القائمة الرئيسية">
        <i class="fas fa-plus"></i>
    </button>
</div>

<!-- السلايد الجانبية للفاتورة السريعة -->
<div class="quick-invoice-sidebar" id="quickInvoiceSidebar">
    <div class="sidebar-header">
        <h5 id="sidebarTitle">فاتورة سريعة</h5>
        <button class="btn-close" onclick="closeQuickInvoice()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <form id="quickInvoiceForm" method="POST" action="process_quick_invoice.php">
            <input type="hidden" id="invoiceType" name="invoice_type" value="">

            <!-- معلومات أساسية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <label for="quickCustomer" class="form-label" id="quickCustomerLabel"><?php echo __('customer'); ?></label>
                        <select class="form-select" id="quickCustomer" name="customer_id" required>
                            <option value=""><?php echo __('select_customer'); ?></option>
                            <option value="new" class="text-success fw-bold"><?php echo __('add_new_customer_option'); ?></option>
                            <!-- سيتم تحديث الخيارات ديناميكياً حسب نوع الفاتورة -->
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-2">
                        <label for="quickDate" class="form-label"><?php echo __('date'); ?></label>
                        <input type="date" class="form-control" id="quickDate" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                </div>
            </div>

            <h6 class="mt-2 mb-1"><?php echo __('invoice_items'); ?></h6>
            <div class="table-responsive">
                <table class="table table-sm" id="quickItemsTable">
                    <thead>
                        <tr>
                            <th><?php echo __('product'); ?></th>
                            <th><?php echo __('quantity'); ?></th>
                            <th><?php echo __('price'); ?></th>
                            <th><?php echo __('tax_rate'); ?>%</th>
                            <th><?php echo __('total'); ?></th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="quickItemsBody">
                        <!-- سيتم إضافة العناصر ديناميكيًا هنا -->
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="6" class="text-center p-1">
                                <button type="button" class="btn btn-xs btn-success" id="addQuickItemBtn">
                                    <i class="fas fa-plus"></i> <?php echo __('add'); ?>
                                </button>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- قسم خيارات الدفع -->
            <div class="row mt-1">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header py-1">
                            <small class="mb-0 fw-bold">
                                <i class="fas fa-credit-card me-1"></i>
                                <?php echo __('payment_options'); ?>
                            </small>
                        </div>
                        <div class="card-body py-2">
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <label for="quickPaymentMethod" class="form-label"><?php echo __('payment_method'); ?></label>
                                    <select class="form-select form-select-sm" id="quickPaymentMethod" name="payment_method" onchange="updateQuickPaymentFields()">
                                        <option value="cash"><?php echo __('cash'); ?></option>
                                        <option value="card"><?php echo __('card'); ?></option>
                                        <option value="bank_transfer"><?php echo __('bank_transfer'); ?></option>
                                        <option value="check"><?php echo __('check'); ?></option>
                                        <option value="other"><?php echo __('other'); ?></option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="quickPaymentStatus" class="form-label"><?php echo __('status'); ?></label>
                                    <select class="form-select form-select-sm" id="quickPaymentStatus" name="payment_status" onchange="updateQuickPaymentFields()">
                                        <option value="unpaid"><?php echo __('unpaid'); ?></option>
                                        <option value="partial"><?php echo __('partial'); ?></option>
                                        <option value="paid"><?php echo __('paid'); ?></option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="quickPaidAmount" class="form-label">مبلغ</label>
                                    <div class="input-group input-group-sm">
                                        <input type="number" class="form-control" id="quickPaidAmount" name="paid_amount"
                                               step="0.01" min="0" value="0" onchange="calculateQuickRemainingAmount()">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-1">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header py-1">
                            <small class="mb-0 fw-bold">ملخص الفاتورة</small>
                        </div>
                        <div class="card-body py-2">
                            <div class="row g-1 text-center">
                                <div class="col-4">
                                    <small class="text-muted">فرعي</small>
                                    <div id="quickSubtotalCell" class="fw-bold">0.00</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">ضريبة</small>
                                    <div id="quickTaxCell" class="fw-bold">0.00</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">إجمالي</small>
                                    <div id="quickTotalCell" class="fw-bold text-primary">0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="quickNotes" class="form-label">ملاحظات</label>
                    <textarea class="form-control form-control-sm" id="quickNotes" name="notes" rows="3" placeholder="ملاحظات..."></textarea>
                </div>
            </div>

            <div class="mt-2 d-flex gap-2">
                <button type="submit" class="btn btn-primary btn-sm flex-fill">
                    <i class="fas fa-save me-1"></i>
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary btn-sm flex-fill" onclick="closeQuickInvoice()">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- خلفية السلايد -->
<div class="sidebar-overlay" id="sidebarOverlay" onclick="closeQuickInvoice()"></div>

<!-- نافذة منبثقة لإضافة عميل/مورد جديد -->
<div class="modal fade" id="quickAddCustomerModal" tabindex="-1" aria-labelledby="quickAddCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddCustomerForm">
                    <input type="hidden" id="quick_customer_type" value="customer">
                    <div class="mb-3">
                        <label for="quick_new_customer_name" class="form-label" id="quickCustomerNameLabel">اسم العميل *</label>
                        <input type="text" class="form-control" id="quick_new_customer_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="quick_new_customer_phone" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="quick_new_customer_phone">
                    </div>
                    <div class="mb-3">
                        <label for="quick_new_customer_email" class="form-label"><?php echo __('email'); ?></label>
                        <input type="email" class="form-control" id="quick_new_customer_email">
                    </div>
                    <div class="mb-3">
                        <label for="quick_new_customer_tax_number" class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-control" id="quick_new_customer_tax_number">
                    </div>
                    <div class="mb-3">
                        <label for="quick_new_customer_address" class="form-label"><?php echo __('address'); ?></label>
                        <textarea class="form-control" id="quick_new_customer_address" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <button type="button" class="btn btn-primary" id="saveQuickNewCustomer"><?php echo __('save'); ?></button>
            </div>
        </div>
    </div>
</div>

<style>

/* الأزرار العائمة */
.floating-buttons {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    gap: 15px;
}

.floating-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.main-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.main-btn.active {
    transform: rotate(45deg);
}

.sale-btn {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.purchase-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

/* السلايد الجانبية */
.quick-invoice-sidebar {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.quick-invoice-sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.sidebar-header .btn-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.sidebar-header .btn-close:hover {
    background: #e9ecef;
    color: #495057;
}

.sidebar-content {
    padding: 15px;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* تحسينات الجدول */
#quickItemsTable {
    margin-bottom: 0;
}

#quickItemsTable th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 10px;
    padding: 4px 3px;
    text-align: center;
}

#quickItemsTable td {
    vertical-align: middle;
    padding: 3px 2px;
}

#quickItemsTable .table-responsive {
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

/* تحسين مربعات النص في الجدول */
#quickItemsTable .form-control,
#quickItemsTable .form-select {
    font-size: 10px;
    padding: 2px 4px;
    height: auto;
    min-height: 24px;
    border: 1px solid #ced4da;
    border-radius: 2px;
}

#quickItemsTable .form-control:focus,
#quickItemsTable .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين عرض الأعمدة */
#quickItemsTable th:nth-child(1),
#quickItemsTable td:nth-child(1) { width: 32%; } /* المنتج */
#quickItemsTable th:nth-child(2),
#quickItemsTable td:nth-child(2) { width: 13%; } /* الكمية */
#quickItemsTable th:nth-child(3),
#quickItemsTable td:nth-child(3) { width: 15%; } /* السعر */
#quickItemsTable th:nth-child(4),
#quickItemsTable td:nth-child(4) { width: 13%; } /* الضريبة */
#quickItemsTable th:nth-child(5),
#quickItemsTable td:nth-child(5) { width: 17%; } /* المجموع */
#quickItemsTable th:nth-child(6),
#quickItemsTable td:nth-child(6) { width: 10%; } /* الإجراءات */

/* تحسين أزرار الحذف */
#quickItemsTable .btn-sm {
    padding: 2px 6px;
    font-size: 10px;
}

/* أزرار صغيرة جداً */
.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
    line-height: 1.2;
}

/* تحسين النصوص في خلايا المجموع */
.quick-row-total {
    font-weight: 600;
    color: #495057;
    text-align: center;
    font-size: 13px;
}

/* تحسين ملخص الفاتورة */
.quick-invoice-sidebar .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-invoice-sidebar .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 12px;
    padding: 8px 12px;
}

.quick-invoice-sidebar .card-body {
    padding: 10px;
}

.quick-invoice-sidebar .card-body .table {
    margin-bottom: 0;
    font-size: 11px;
}

.quick-invoice-sidebar .card-body .table th,
.quick-invoice-sidebar .card-body .table td {
    padding: 6px 8px;
    border-top: 1px solid #dee2e6;
}

.quick-invoice-sidebar .card-body .table th {
    font-weight: 600;
    color: #495057;
}

.quick-invoice-sidebar .card-body .table td {
    text-align: right;
    font-weight: 500;
    color: #007bff;
}

/* تحسين النماذج العامة */
.quick-invoice-sidebar .form-control,
.quick-invoice-sidebar .form-select {
    font-size: 12px;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.quick-invoice-sidebar .form-control:focus,
.quick-invoice-sidebar .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.quick-invoice-sidebar .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    font-size: 11px;
}

/* تحسين الأزرار */
.quick-invoice-sidebar .btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.quick-invoice-sidebar .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.quick-invoice-sidebar .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.quick-invoice-sidebar .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
    border: none;
}

.quick-invoice-sidebar .btn-secondary:hover {
    background: linear-gradient(135deg, #545b62, #3d4142);
    transform: translateY(-1px);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .quick-invoice-sidebar {
        width: 100%;
        right: -100%;
    }

    .floating-buttons {
        bottom: 80px;
        right: 20px;
    }

    .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    /* تحسين الجدول للشاشات الصغيرة */
    #quickItemsTable th,
    #quickItemsTable td {
        padding: 6px 4px;
        font-size: 12px;
    }

    #quickItemsTable .form-control,
    #quickItemsTable .form-select {
        font-size: 12px;
        padding: 4px 6px;
        min-height: 28px;
    }
}

/* تأثيرات إضافية */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين مظهر الأزرار */
.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62, #3d4142);
    transform: translateY(-1px);
}
</style>

<script>
let itemCounter = 0;
let floatingMenuOpen = false;

// بيانات المنتجات (سيتم تحميلها من قاعدة البيانات)
const quickProducts = [
    <?php
    try {
        $db = resetDBConnection($db);

        // التحقق من وجود عمود is_active
        $columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
        $has_is_active = $columns_check && $columns_check->num_rows > 0;

        if ($has_is_active) {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products WHERE is_active = 1 ORDER BY category, name");
        } else {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
        }

        if ($products_result && $products_result->num_rows > 0) {
            while ($product = $products_result->fetch_assoc()) {
                $category = $product['category'] ? addslashes($product['category']) : '';
                echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}, category: '$category'},";
            }
        }
    } catch (Exception $e) {
        error_log("Error loading products: " . $e->getMessage());
    }
    ?>
];

// فتح/إغلاق القائمة العائمة
function toggleFloatingMenu() {
    console.log('toggleFloatingMenu called');

    const saleBtn = document.getElementById('saleBtn');
    const purchaseBtn = document.getElementById('purchaseBtn');
    const mainBtn = document.querySelector('.main-btn');

    if (!saleBtn || !purchaseBtn || !mainBtn) {
        console.error('Floating buttons not found');
        return;
    }

    floatingMenuOpen = !floatingMenuOpen;
    console.log('floatingMenuOpen:', floatingMenuOpen);

    if (floatingMenuOpen) {
        saleBtn.style.display = 'flex';
        purchaseBtn.style.display = 'flex';
        mainBtn.classList.add('active');
        console.log('Buttons shown');
    } else {
        saleBtn.style.display = 'none';
        purchaseBtn.style.display = 'none';
        mainBtn.classList.remove('active');
        console.log('Buttons hidden');
    }
}

// فتح السلايد الجانبية
function openQuickInvoice(type) {
    console.log('openQuickInvoice called with type:', type);

    // إغلاق القائمة العائمة أولاً
    if (floatingMenuOpen) {
        toggleFloatingMenu();
    }

    const sidebar = document.getElementById('quickInvoiceSidebar');
    const overlay = document.getElementById('sidebarOverlay');
    const title = document.getElementById('sidebarTitle');
    const typeInput = document.getElementById('invoiceType');

    if (!sidebar || !overlay || !title || !typeInput) {
        console.error('Required elements not found for quick invoice');
        alert(`فتح فاتورة ${type === 'sale' ? 'المبيعات' : 'المشتريات'}`);
        return;
    }

    // تحديد نوع الفاتورة
    typeInput.value = type;

    if (type === 'sale') {
        title.innerHTML = '<i class="fas fa-file-invoice-dollar me-2"></i>فاتورة مبيعات سريعة';

        // تحديث تسميات العميل
        const customerLabel = document.getElementById('quickCustomerLabel');
        if (customerLabel) {
            customerLabel.textContent = '<?php echo __("customer"); ?>';
        }

        // تحديث نافذة إضافة عميل جديد
        updateQuickAddCustomerModal('customer');

    } else {
        title.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>فاتورة مشتريات سريعة';

        // تحديث تسميات المورد
        const customerLabel = document.getElementById('quickCustomerLabel');
        if (customerLabel) {
            customerLabel.textContent = '<?php echo __("supplier"); ?>';
        }

        // تحديث نافذة إضافة مورد جديد
        updateQuickAddCustomerModal('supplier');
    }

    // تحديث قائمة العملاء/الموردين (إذا كان العنصر موجود)
    try {
        if (document.getElementById('quickCustomer')) {
            updateCustomersList(type);
        }
    } catch (error) {
        console.log('Error updating customers list:', error);
    }

    // إظهار السلايد
    sidebar.classList.add('active');
    overlay.classList.add('active');

    // منع التمرير في الخلفية
    document.body.style.overflow = 'hidden';

    console.log('Quick invoice opened successfully');

    // إضافة أول صف للأصناف
    setTimeout(() => {
        addQuickItemRow();
        updateQuickInvoiceSummary();
    }, 100);
}

// دالة تحديث نافذة إضافة عميل/مورد جديد
function updateQuickAddCustomerModal(customerType) {
    const modalTitle = document.getElementById('quickAddCustomerModalLabel');
    const nameLabel = document.getElementById('quickCustomerNameLabel');
    const customerTypeInput = document.getElementById('quick_customer_type');

    if (customerType === 'supplier') {
        if (modalTitle) modalTitle.textContent = '<?php echo __("add_supplier"); ?>';
        if (nameLabel) nameLabel.textContent = 'اسم المورد *';
        if (customerTypeInput) customerTypeInput.value = 'supplier';
    } else {
        if (modalTitle) modalTitle.textContent = '<?php echo __("add_customer"); ?>';
        if (nameLabel) nameLabel.textContent = 'اسم العميل *';
        if (customerTypeInput) customerTypeInput.value = 'customer';
    }

    console.log('Updated add customer modal for type:', customerType);
}

// دالة تحديث قائمة العملاء/الموردين حسب نوع الفاتورة
function updateCustomersList(invoiceType) {
    const customerSelect = document.getElementById('quickCustomer');

    // التحقق من وجود العنصر
    if (!customerSelect) {
        console.log('quickCustomer element not found, skipping update');
        return;
    }

    const customerType = invoiceType === 'sale' ? 'customer' : 'supplier';
    console.log('Updating customers list for type:', customerType);

    // مسح الخيارات الحالية (عدا الخيارات الثابتة)
    while (customerSelect.children.length > 2) {
        customerSelect.removeChild(customerSelect.lastChild);
    }

    // جلب العملاء/الموردين من الخادم
    fetch(`get_customers_by_type.php?type=${customerType}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Customers data received:', data);
            if (data.success && data.customers) {
                data.customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    if (customer.phone) {
                        option.textContent += ` - ${customer.phone}`;
                    }
                    customerSelect.appendChild(option);
                });
                console.log(`Added ${data.customers.length} ${customerType}s to list`);
            } else {
                console.log('No customers found or error in response');
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
            // لا نوقف تشغيل باقي الوظائف بسبب هذا الخطأ
        });
}

// إغلاق السلايد الجانبية
function closeQuickInvoice() {
    const sidebar = document.getElementById('quickInvoiceSidebar');
    const overlay = document.getElementById('sidebarOverlay');

    sidebar.classList.remove('active');
    overlay.classList.remove('active');

    // السماح بالتمرير مرة أخرى
    document.body.style.overflow = 'auto';

    // إعادة تعيين النموذج
    document.getElementById('quickInvoiceForm').reset();

    // إعادة تعيين الأصناف
    const itemsBody = document.getElementById('quickItemsBody');
    itemsBody.innerHTML = '';

    itemCounter = 0;
    updateQuickInvoiceSummary();
}

// دالة لإضافة صف عنصر جديد
function addQuickItemRow(product = null) {
    const tbody = document.getElementById('quickItemsBody');
    const rowId = Date.now() + itemCounter;
    itemCounter++;

    const row = document.createElement('tr');
    row.id = `quick_row_${rowId}`;

    // عمود المنتج
    const productCell = document.createElement('td');
    const productSelect = document.createElement('select');
    productSelect.className = 'form-select quick-product-select';
    productSelect.name = 'product_id[]';
    productSelect.required = true;

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر منتج --';
    productSelect.appendChild(defaultOption);

    // إضافة خيار "إضافة منتج جديد"
    const addNewOption = document.createElement('option');
    addNewOption.value = 'new';
    addNewOption.textContent = '-- إضافة منتج جديد --';
    addNewOption.className = 'text-success fw-bold';
    productSelect.appendChild(addNewOption);

    quickProducts.forEach(p => {
        const option = document.createElement('option');
        option.value = p.id;
        // عرض اسم المنتج مع التصنيف إذا كان متوفراً
        const displayName = p.category ? `${p.name} (${p.category})` : p.name;
        option.textContent = displayName;
        option.dataset.price = p.price;
        option.dataset.taxRate = p.tax_rate;
        option.dataset.category = p.category || '';

        if (product && product.id == p.id) {
            option.selected = true;
        }

        productSelect.appendChild(option);
    });

    productSelect.addEventListener('change', function() {
        const selectedValue = this.value;
        const row = this.closest('tr');

        if (selectedValue === 'new') {
            // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
            this.value = '';
            alert('ميزة إضافة منتج جديد ستكون متاحة قريباً');
        } else if (selectedValue !== '') {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price || 0;
            const taxRate = selectedOption.dataset.taxRate || 15;

            row.querySelector('.quick-price-input').value = price;
            row.querySelector('.quick-tax-rate-input').value = taxRate;

            calculateQuickRowTotal(row);
            updateQuickInvoiceSummary();
        }
    });

    productCell.appendChild(productSelect);
    row.appendChild(productCell);

    // عمود الكمية
    const quantityCell = document.createElement('td');
    const quantityInput = document.createElement('input');
    quantityInput.type = 'number';
    quantityInput.className = 'form-control quick-quantity-input';
    quantityInput.name = 'quantity[]';
    quantityInput.min = '1';
    quantityInput.step = '0.01';
    quantityInput.value = product ? product.quantity : '1';
    quantityInput.required = true;
    quantityInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    quantityCell.appendChild(quantityInput);
    row.appendChild(quantityCell);

    // عمود السعر
    const priceCell = document.createElement('td');
    const priceInput = document.createElement('input');
    priceInput.type = 'number';
    priceInput.className = 'form-control quick-price-input';
    priceInput.name = 'price[]';
    priceInput.step = '0.01';
    priceInput.min = '0';
    priceInput.value = product ? product.unit_price : '0';
    priceInput.required = true;
    priceInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    priceCell.appendChild(priceInput);
    row.appendChild(priceCell);

    // عمود الضريبة
    const taxCell = document.createElement('td');
    const taxInput = document.createElement('input');
    taxInput.type = 'number';
    taxInput.className = 'form-control quick-tax-rate-input';
    taxInput.name = 'tax_rate[]';
    taxInput.step = '0.01';
    taxInput.min = '0';
    taxInput.value = product ? product.tax_rate : '15';
    taxInput.required = true;
    taxInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    taxCell.appendChild(taxInput);
    row.appendChild(taxCell);

    // عمود المجموع
    const totalCell = document.createElement('td');
    totalCell.className = 'quick-row-total';
    totalCell.textContent = '0.00 ر.س';
    row.appendChild(totalCell);

    // عمود الإجراءات
    const actionsCell = document.createElement('td');
    const deleteBtn = document.createElement('button');
    deleteBtn.type = 'button';
    deleteBtn.className = 'btn btn-sm btn-danger';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.addEventListener('click', function() {
        row.remove();
        updateQuickInvoiceSummary();
    });
    actionsCell.appendChild(deleteBtn);
    row.appendChild(actionsCell);

    tbody.appendChild(row);

    // إذا تم تمرير منتج، احسب المجموع للصف
    if (product) {
        calculateQuickRowTotal(row);
    }

    return row;
}

// دالة لحساب مجموع الصف
function calculateQuickRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quick-quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.quick-price-input').value) || 0;
    const taxRate = parseFloat(row.querySelector('.quick-tax-rate-input').value) || 0;

    const subtotal = quantity * price;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    row.querySelector('.quick-row-total').textContent = total.toFixed(2) + ' ر.س';
}

// دالة لتحديث ملخص الفاتورة
function updateQuickInvoiceSummary() {
    let subtotal = 0;
    let taxAmount = 0;

    document.querySelectorAll('#quickItemsBody tr').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quick-quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.quick-price-input').value) || 0;
        const taxRate = parseFloat(row.querySelector('.quick-tax-rate-input').value) || 0;

        const rowSubtotal = quantity * price;
        const rowTax = rowSubtotal * (taxRate / 100);

        subtotal += rowSubtotal;
        taxAmount += rowTax;
    });

    const total = subtotal + taxAmount;

    document.getElementById('quickSubtotalCell').textContent = subtotal.toFixed(2);
    document.getElementById('quickTaxCell').textContent = taxAmount.toFixed(2);
    document.getElementById('quickTotalCell').textContent = total.toFixed(2);

    // تحديث حقول الدفع تلقائياً
    updateQuickPaymentFields();
}

// دالة لتحديث حقول الدفع بناءً على حالة الدفع في الفاتورة السريعة
function updateQuickPaymentFields() {
    const paymentStatusElement = document.getElementById('quickPaymentStatus');
    const paidAmountField = document.getElementById('quickPaidAmount');
    const totalAmount = parseFloat(document.getElementById('quickTotalCell').textContent) || 0;

    // التحقق من وجود العناصر
    if (!paymentStatusElement || !paidAmountField) {
        console.log('Payment fields not found, skipping update');
        return;
    }

    const paymentStatus = paymentStatusElement.value;

    if (paymentStatus === 'paid') {
        // إذا كان مدفوع بالكامل، اجعل المبلغ المدفوع = الإجمالي
        paidAmountField.value = totalAmount.toFixed(2);
    } else if (paymentStatus === 'unpaid') {
        // إذا كان غير مدفوع، اجعل المبلغ المدفوع = 0
        paidAmountField.value = '0.00';
    } else if (paymentStatus === 'partial') {
        // إذا كان مدفوع جزئياً، اتركه كما هو أو اجعله نصف المبلغ
        if (parseFloat(paidAmountField.value) === 0) {
            paidAmountField.value = (totalAmount / 2).toFixed(2);
        }
    }

    calculateQuickRemainingAmount();
}

// دالة لحساب المبلغ المتبقي في الفاتورة السريعة
function calculateQuickRemainingAmount() {
    const totalCell = document.getElementById('quickTotalCell');
    const paidAmountField = document.getElementById('quickPaidAmount');

    // التحقق من وجود العناصر
    if (!totalCell || !paidAmountField) {
        console.log('Required elements for remaining amount calculation not found');
        return;
    }

    const totalAmount = parseFloat(totalCell.textContent) || 0;
    const paidAmount = parseFloat(paidAmountField.value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    // عرض المبلغ المتبقي في ملخص الفاتورة (اختياري)
    let remainingCell = document.getElementById('quickRemainingCell');
    if (remainingCell) {
        remainingCell.textContent = remainingAmount.toFixed(2) + ' ر.س';

        // تغيير لون النص حسب الحالة
        if (remainingAmount > 0) {
            remainingCell.className = 'text-danger fw-bold';
        } else if (remainingAmount < 0) {
            remainingCell.className = 'text-warning fw-bold';
        } else {
            remainingCell.className = 'text-success fw-bold';
        }
    }
}

// دالة لإضافة عميل/مورد جديد إلى قاعدة البيانات
async function saveQuickNewCustomer() {
    const customerName = document.getElementById('quick_new_customer_name').value.trim();
    const customerPhone = document.getElementById('quick_new_customer_phone').value.trim();
    const customerEmail = document.getElementById('quick_new_customer_email').value.trim();
    const customerTaxNumber = document.getElementById('quick_new_customer_tax_number').value.trim();
    const customerAddress = document.getElementById('quick_new_customer_address').value.trim();
    const customerType = document.getElementById('quick_customer_type').value || 'customer';

    const entityName = customerType === 'supplier' ? 'المورد' : 'العميل';

    if (!customerName) {
        alert(`يرجى إدخال اسم ${entityName}`);
        return;
    }

    if (customerName.length < 2) {
        alert(`اسم ${entityName} يجب أن يكون أكثر من حرف واحد`);
        return;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (customerEmail && !isValidEmail(customerEmail)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }

    try {
        // إرسال بيانات العميل الجديد إلى الخادم
        const formData = new FormData();
        formData.append('name', customerName);
        formData.append('phone', customerPhone);
        formData.append('email', customerEmail);
        formData.append('tax_number', customerTaxNumber);
        formData.append('address', customerAddress);
        formData.append('customer_type', customerType);
        formData.append('action', 'add_customer');

        const response = await fetch('ajax_handler.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const result = await response.json();

        if (result.success) {
            // إضافة العميل الجديد إلى القائمة المنسدلة
            const customerSelect = document.getElementById('quickCustomer');
            const option = document.createElement('option');
            option.value = result.customer_id;
            option.textContent = customerName;
            option.selected = true;

            // إضافة الخيار قبل الخيار الأخير (إضافة عميل جديد)
            customerSelect.insertBefore(option, customerSelect.options[1]);

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('quickAddCustomerModal'));
            modal.hide();

            // إعادة تعيين نموذج إضافة العميل
            document.getElementById('quickAddCustomerForm').reset();

            // عرض رسالة نجاح
            alert(`تم إضافة ${entityName} بنجاح`);
        } else {
            console.error('Server error:', result);
            alert(`حدث خطأ أثناء إضافة ${entityName}: ` + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error saving customer:', error);
        alert(`حدث خطأ في الشبكة أثناء إضافة ${entityName}. يرجى المحاولة مرة أخرى.`);
    }
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // معالجة زر إضافة عنصر
    document.getElementById('addQuickItemBtn').addEventListener('click', function() {
        addQuickItemRow();
    });

    // معالجة زر حفظ العميل الجديد
    document.getElementById('saveQuickNewCustomer').addEventListener('click', saveQuickNewCustomer);

    // معالجة تغيير قائمة العملاء
    document.getElementById('quickCustomer').addEventListener('change', function() {
        if (this.value === 'new') {
            // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
            this.value = '';

            // فتح النافذة المنبثقة لإضافة عميل جديد
            const modal = new bootstrap.Modal(document.getElementById('quickAddCustomerModal'));
            modal.show();
        }
    });



    // إغلاق السلايد بالضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeQuickInvoice();
            if (floatingMenuOpen) {
                toggleFloatingMenu();
            }
        }
    });

    // إغلاق القائمة العائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        const floatingButtons = document.querySelector('.floating-buttons');
        if (floatingMenuOpen && floatingButtons && !floatingButtons.contains(e.target)) {
            toggleFloatingMenu();
        }
    });

    // إرسال النموذج
    document.getElementById('quickInvoiceForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // التحقق من وجود أصناف
        const items = document.querySelectorAll('#quickItemsBody tr');
        if (items.length === 0) {
            alert('يجب إضافة عنصر واحد على الأقل');
            return;
        }

        // التحقق من ملء جميع الحقول المطلوبة
        let isValid = true;
        let hasValidItem = false;

        items.forEach(item => {
            const productSelect = item.querySelector('.quick-product-select');
            const quantity = item.querySelector('.quick-quantity-input');
            const price = item.querySelector('.quick-price-input');

            if (productSelect && quantity && price) {
                const productValue = productSelect.value;
                const quantityValue = quantity.value;
                const priceValue = price.value;

                if (productValue && quantityValue && priceValue) {
                    hasValidItem = true;
                } else if (productValue || quantityValue || priceValue) {
                    // إذا كان هناك قيم جزئية، فهذا خطأ
                    isValid = false;
                }
            }
        });

        if (!hasValidItem) {
            alert('يجب إضافة عنصر واحد على الأقل مع ملء جميع الحقول');
            return;
        }

        if (!isValid) {
            alert('يرجى ملء جميع حقول العناصر أو حذف العناصر الفارغة');
            return;
        }

        // التحقق من العميل/المورد
        const customer = document.getElementById('quickCustomer').value;
        const invoiceType = document.getElementById('invoiceType').value;
        const entityName = invoiceType === 'sale' ? 'العميل' : 'المورد';

        if (!customer) {
            alert(`يرجى اختيار ${entityName}`);
            return;
        }

        // إرسال النموذج
        this.submit();
    });
});

// متغيرات عامة للنوافذ المنبثقة
let currentInvoiceId = null;
let currentInvoiceType = null;

// دالة عرض الفاتورة في نافذة منبثقة
function viewInvoice(invoiceId, type) {
    currentInvoiceId = invoiceId;
    currentInvoiceType = type;

    // إظهار النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
    modal.show();

    // تحميل محتوى الفاتورة
    const contentDiv = document.getElementById('viewInvoiceContent');
    contentDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    // طلب AJAX لتحميل محتوى الفاتورة
    fetch(`view_${type}.php?id=${invoiceId}&modal=1`)
        .then(response => response.text())
        .then(data => {
            contentDiv.innerHTML = data;
        })
        .catch(error => {
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل الفاتورة
                </div>
            `;
        });
}

// دالة طباعة الفاتورة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('printInvoiceBtn').addEventListener('click', function() {
        if (currentInvoiceId && currentInvoiceType) {
            window.open(`print_invoice.php?id=${currentInvoiceId}&type=${currentInvoiceType}`, '_blank');
        }
    });
});
</script>

<!-- النوافذ المنبثقة -->
<!-- نافذة عرض الفاتورة -->
<div class="modal fade" id="viewInvoiceModal" tabindex="-1" aria-labelledby="viewInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title" id="viewInvoiceModalLabel">
                    <i class="fas fa-eye me-1"></i>
                    عرض الفاتورة
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-2">
                <div id="viewInvoiceContent">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-sm btn-primary" id="printInvoiceBtn">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php';
$db->close(); ?>