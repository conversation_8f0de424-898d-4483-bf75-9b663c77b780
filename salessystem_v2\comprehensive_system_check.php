<?php
/**
 * فحص شامل ودقيق للمشروع بالكامل
 */

// تعطيل عرض الأخطاء لتجنب التداخل مع التقرير
error_reporting(E_ALL);
ini_set('display_errors', 0);

echo "<h1>🔍 فحص شامل للمشروع</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f8f9fa; }
    .critical { background-color: #f8d7da; }
    .good { background-color: #d4edda; }
</style>";

$errors = [];
$warnings = [];
$info = [];

// 1. فحص الملفات الأساسية
echo "<div class='section'>";
echo "<h2>1. فحص الملفات الأساسية</h2>";

$core_files = [
    'config/init.php' => 'ملف التهيئة الرئيسي',
    'config/unified_db_config.php' => 'ملف قاعدة البيانات الموحدة',
    'includes/functions.php' => 'ملف الدوال العامة',
    'includes/error_handler.php' => 'ملف معالج الأخطاء',
    'includes/header.php' => 'ملف الرأس',
    'includes/footer.php' => 'ملف التذييل',
    'includes/admin_header.php' => 'ملف رأس المدير',
    'includes/admin_footer.php' => 'ملف تذييل المدير'
];

foreach ($core_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description: موجود</p>";
        
        // فحص الأخطاء التركيبية
        $syntax_check = shell_exec("php -l \"$file\" 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "<p class='success'>✅ $description: لا توجد أخطاء تركيبية</p>";
        } else {
            echo "<p class='error'>❌ $description: خطأ تركيبي - $syntax_check</p>";
            $errors[] = "خطأ تركيبي في $file: $syntax_check";
        }
    } else {
        echo "<p class='error'>❌ $description: غير موجود</p>";
        $errors[] = "ملف مفقود: $file";
    }
}
echo "</div>";

// 2. فحص قاعدة البيانات
echo "<div class='section'>";
echo "<h2>2. فحص قاعدة البيانات</h2>";

try {
    require_once 'config/init.php';
    
    $db = getUnifiedDB();
    if ($db) {
        echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
        
        // فحص الجداول المطلوبة
        $required_tables = [
            'users' => 'جدول المستخدمين',
            'admins' => 'جدول المديرين', 
            'customers' => 'جدول العملاء',
            'products' => 'جدول المنتجات',
            'sales' => 'جدول المبيعات',
            'purchases' => 'جدول المشتريات',
            'sale_items' => 'جدول أصناف المبيعات',
            'purchase_items' => 'جدول أصناف المشتريات',
            'system_settings' => 'جدول إعدادات النظام',
            'activity_log' => 'جدول سجل الأنشطة'
        ];
        
        echo "<h3>الجداول:</h3>";
        foreach ($required_tables as $table => $description) {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                // فحص عدد السجلات
                $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                echo "<p class='success'>✅ $description: موجود ($count سجل)</p>";
            } else {
                echo "<p class='error'>❌ $description: غير موجود</p>";
                $errors[] = "جدول مفقود: $table";
            }
        }
        
        // فحص المدير الرئيسي
        $admin_check = $db->query("SELECT COUNT(*) as count FROM admins WHERE role = 'super_admin'");
        if ($admin_check) {
            $admin_count = $admin_check->fetch_assoc()['count'];
            if ($admin_count > 0) {
                echo "<p class='success'>✅ المدير الرئيسي: موجود</p>";
            } else {
                echo "<p class='warning'>⚠️ المدير الرئيسي: غير موجود</p>";
                $warnings[] = "لا يوجد مدير رئيسي في النظام";
            }
        }
        
    } else {
        echo "<p class='error'>❌ الاتصال بقاعدة البيانات: فشل</p>";
        $errors[] = "فشل في الاتصال بقاعدة البيانات";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
echo "</div>";

// 3. فحص الدوال المطلوبة
echo "<div class='section'>";
echo "<h2>3. فحص الدوال المطلوبة</h2>";

$required_functions = [
    'getUnifiedDB' => 'دالة الاتصال بقاعدة البيانات',
    'isAdminLoggedIn' => 'دالة فحص تسجيل دخول المدير',
    'hasAdminPermission' => 'دالة فحص صلاحيات المدير',
    'getCurrentAdmin' => 'دالة الحصول على المدير الحالي',
    'getSystemSettings' => 'دالة جلب إعدادات النظام',
    'updateSystemSetting' => 'دالة تحديث إعدادات النظام',
    'getSystemSetting' => 'دالة الحصول على إعداد محدد',
    'generateInvoiceNumber' => 'دالة إنشاء رقم فاتورة',
    'displayMessages' => 'دالة عرض الرسائل',
    'createAppTables' => 'دالة إنشاء الجداول',
    'ensureSuperAdminExists' => 'دالة التأكد من وجود المدير الرئيسي'
];

foreach ($required_functions as $func => $description) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ $description: موجودة</p>";
    } else {
        echo "<p class='error'>❌ $description: غير موجودة</p>";
        $errors[] = "دالة مفقودة: $func";
    }
}
echo "</div>";

// 4. فحص الصفحات الرئيسية
echo "<div class='section'>";
echo "<h2>4. فحص الصفحات الرئيسية</h2>";

$main_pages = [
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'register.php' => 'صفحة التسجيل',
    'admin_login.php' => 'صفحة تسجيل دخول المدير',
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'admin_system.php' => 'صفحة إعدادات النظام',
    'admin_manage_admins.php' => 'صفحة إدارة المديرين',
    'admin_users.php' => 'صفحة إدارة المستخدمين',
    'customers.php' => 'صفحة العملاء',
    'products.php' => 'صفحة المنتجات',
    'sales.php' => 'صفحة المبيعات',
    'purchases.php' => 'صفحة المشتريات',
    'reports.php' => 'صفحة التقارير'
];

foreach ($main_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<p class='success'>✅ $description: موجودة</p>";
        
        // فحص الأخطاء التركيبية
        $syntax_check = shell_exec("php -l \"$page\" 2>&1");
        if (strpos($syntax_check, 'No syntax errors') === false) {
            echo "<p class='error'>❌ $description: خطأ تركيبي</p>";
            $errors[] = "خطأ تركيبي في $page";
        }
    } else {
        echo "<p class='warning'>⚠️ $description: غير موجودة</p>";
        $warnings[] = "صفحة مفقودة: $page";
    }
}
echo "</div>";

// 5. فحص الأذونات والمجلدات
echo "<div class='section'>";
echo "<h2>5. فحص المجلدات والأذونات</h2>";

$directories = [
    'config' => 'مجلد الإعدادات',
    'includes' => 'مجلد الملفات المضمنة',
    'assets' => 'مجلد الأصول',
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الرفع',
    'backups' => 'مجلد النسخ الاحتياطية'
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        $class = is_writable($dir) ? 'success' : 'warning';
        echo "<p class='$class'>✅ $description: موجود ($writable)</p>";
        
        if (!is_writable($dir) && in_array($dir, ['logs', 'uploads', 'backups'])) {
            $warnings[] = "مجلد $dir غير قابل للكتابة";
        }
    } else {
        echo "<p class='error'>❌ $description: غير موجود</p>";
        $errors[] = "مجلد مفقود: $dir";
    }
}
echo "</div>";

// 6. ملخص النتائج
echo "<div class='section'>";
echo "<h2>6. ملخص النتائج</h2>";

echo "<h3>📊 الإحصائيات:</h3>";
echo "<p><strong>إجمالي الأخطاء:</strong> " . count($errors) . "</p>";
echo "<p><strong>إجمالي التحذيرات:</strong> " . count($warnings) . "</p>";

if (!empty($errors)) {
    echo "<h3 class='error'>❌ الأخطاء الحرجة:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li class='error'>$error</li>";
    }
    echo "</ul>";
}

if (!empty($warnings)) {
    echo "<h3 class='warning'>⚠️ التحذيرات:</h3>";
    echo "<ul>";
    foreach ($warnings as $warning) {
        echo "<li class='warning'>$warning</li>";
    }
    echo "</ul>";
}

if (empty($errors) && empty($warnings)) {
    echo "<h3 class='success'>🎉 ممتاز! لا توجد أخطاء أو تحذيرات</h3>";
    echo "<p class='success'>النظام يعمل بشكل مثالي ولا يحتاج إلى أي إصلاحات.</p>";
} elseif (empty($errors)) {
    echo "<h3 class='info'>✅ النظام يعمل بشكل جيد</h3>";
    echo "<p class='info'>توجد بعض التحذيرات البسيطة التي يمكن تجاهلها أو إصلاحها لاحقاً.</p>";
} else {
    echo "<h3 class='error'>🚨 يحتاج النظام إلى إصلاحات</h3>";
    echo "<p class='error'>توجد أخطاء حرجة تحتاج إلى إصلاح فوري.</p>";
}

echo "</div>";

// 7. أدوات الإصلاح السريع
echo "<div class='section'>";
echo "<h2>7. أدوات الإصلاح السريع</h2>";

echo "<h3>🛠️ أدوات متاحة:</h3>";
echo "<ul>";
echo "<li><a href='fix_database_issues.php'>إصلاح مشاكل قاعدة البيانات</a></li>";
echo "<li><a href='fix_admin_pages.php'>إصلاح صفحات المدير</a></li>";
echo "<li><a href='test_functions.php'>اختبار الدوال</a></li>";
echo "<li><a href='test_system.php'>اختبار النظام الشامل</a></li>";
echo "<li><a href='auto_fix_database.php'>الإصلاح التلقائي لقاعدة البيانات</a></li>";
echo "</ul>";

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='admin_login.php'>تسجيل دخول المدير</a></li>";
echo "<li><a href='admin_dashboard.php'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php'>إدارة المديرين</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s') . " | ";
echo "الذاكرة المستخدمة: " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB";
echo "</p>";

?>
