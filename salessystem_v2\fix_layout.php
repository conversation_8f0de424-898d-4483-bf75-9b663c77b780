<?php
/**
 * سكريپت إصلاح مشكلة التخطيط في جميع صفحات المدير
 */

// قائمة صفحات المدير
$admin_pages = [
    'admin_activity.php',
    'admin_reports.php', 
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixes_applied = [];
$errors = [];

echo "<h2>إصلاح مشكلة التخطيط في صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إصلاح المحتوى الرئيسي - النمط الأول
    $pattern1 = '/class="col-lg-9 ms-auto px-4 py-3"\s*style="[^"]*"/';
    if (preg_match($pattern1, $content)) {
        $content = preg_replace($pattern1, 'class="admin-content"', $content);
        $changes_made = true;
        echo "✅ تم إصلاح المحتوى الرئيسي (النمط الأول)<br>";
    }
    
    // 2. إصلاح المحتوى الرئيسي - النمط الثاني
    $pattern2 = '/class="col-md-9 ms-sm-auto col-lg-10 px-md-4"/';
    if (preg_match($pattern2, $content)) {
        $content = preg_replace($pattern2, 'class="admin-content"', $content);
        $changes_made = true;
        echo "✅ تم إصلاح المحتوى الرئيسي (النمط الثاني)<br>";
    }
    
    // 3. إصلاح أي style inline للمحتوى الرئيسي
    $pattern3 = '/style="margin-right:\s*300px[^"]*"/';
    if (preg_match($pattern3, $content)) {
        $content = preg_replace($pattern3, '', $content);
        $changes_made = true;
        echo "✅ تم إزالة الـ inline styles<br>";
    }
    
    // 4. إصلاح container-fluid إذا كان موجود
    $pattern4 = '/<div class="container-fluid">\s*<div class="row">/';
    if (preg_match($pattern4, $content)) {
        $content = preg_replace($pattern4, '<div class="admin-layout">', $content);
        $changes_made = true;
        echo "✅ تم إصلاح container-fluid<br>";
    }
    
    // 5. إصلاح إغلاق divs المتعددة في النهاية
    $pattern5 = '/<\/main>\s*<\/div>\s*<\/div>/';
    if (preg_match($pattern5, $content)) {
        $content = preg_replace($pattern5, '</main></div>', $content);
        $changes_made = true;
        echo "✅ تم إصلاح إغلاق الـ divs<br>";
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.layout_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixes_applied[] = $page;
            echo "✅ تم حفظ الإصلاحات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ الإصلاحات<br>";
        }
    } else {
        echo "ℹ️ لا توجد إصلاحات مطلوبة<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>ملخص الإصلاحات:</h3>";

if (!empty($fixes_applied)) {
    echo "<div style='color: green;'>";
    echo "<h4>✅ الملفات المُصلحة بنجاح:</h4>";
    foreach ($fixes_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<br><h3>الإصلاحات المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ تحويل المحتوى الرئيسي إلى class='admin-content'</li>";
echo "<li>✅ إزالة الـ inline styles المسببة للمشاكل</li>";
echo "<li>✅ إصلاح container-fluid إلى admin-layout</li>";
echo "<li>✅ تنظيف إغلاق الـ divs الزائدة</li>";
echo "<li>✅ تحسين الاستجابة للشاشات المختلفة</li>";
echo "</ul>";

echo "<br><div style='background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50;'>";
echo "<h4>📋 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>تم إنشاء نسخ احتياطية لجميع الملفات المُعدلة</li>";
echo "<li>التخطيط الجديد يستخدم CSS محسن في admin_header_new.php</li>";
echo "<li>الشريط الجانبي الآن ثابت على اليمين</li>";
echo "<li>المحتوى يتكيف تلقائياً مع حجم الشاشة</li>";
echo "<li>تم إصلاح مشكلة التمرير والإزاحة</li>";
echo "</ul>";
echo "</div>";

echo "<br><a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>العودة للوحة التحكم</a>";
echo " ";
echo "<a href='admin_users.php' style='background: #4facfe; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin-left: 10px;'>اختبار إدارة المستخدمين</a>";
?>
