/**
 * أزرار الفواتير السريعة العائمة
 * يتم تضمينها في جميع صفحات النظام
 */

/* الأزرار العائمة */
.floating-buttons {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    gap: 15px;
}

.floating-btn {
    width: 100px;
    height: 50px;
    border-radius: 25px;
    border: none;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    flex-direction: row;
    gap: 6px;
    font-weight: 600;
    padding: 0 12px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.floating-btn i {
    font-size: 16px;
    flex-shrink: 0;
}

.btn-label {
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.floating-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    text-decoration: none;
    color: white;
}

.floating-btn:focus {
    outline: none;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    color: white;
}

/* ألوان الأزرار */
.sale-btn {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.sale-btn:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
}

.purchase-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.purchase-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
}

/* تأثيرات إضافية */
.floating-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .floating-buttons {
        bottom: 80px;
        right: 15px;
        gap: 12px;
    }

    .floating-btn {
        width: 90px;
        height: 45px;
        font-size: 12px;
        gap: 5px;
        padding: 0 8px;
    }

    .floating-btn:hover {
        transform: translateY(-1px) scale(1.02);
    }

    .btn-label {
        font-size: 11px;
    }

    .floating-btn i {
        font-size: 14px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .floating-buttons {
        bottom: 60px;
        right: 10px;
        gap: 8px;
    }

    .floating-btn {
        width: 80px;
        height: 40px;
        font-size: 11px;
        gap: 4px;
        padding: 0 6px;
        border-radius: 20px;
    }

    .floating-btn:hover {
        transform: translateY(-1px) scale(1.02);
    }

    .btn-label {
        font-size: 10px;
    }

    .floating-btn i {
        font-size: 12px;
    }
}

/* تحسينات للطباعة - إخفاء الأزرار */
@media print {
    .floating-buttons {
        display: none !important;
    }
}

/* تحسينات للوضع الليلي */
@media (prefers-color-scheme: dark) {
    .floating-btn {
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }

    .floating-btn:hover {
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.15);
    }
}

/* تأثيرات الحركة */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

.floating-btn {
    animation: float 3s ease-in-out infinite;
}

.floating-btn:hover {
    animation: none;
}

/* تأثير النبض للفت الانتباه */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    }
    100% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

.floating-btn.pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* تحسينات إمكانية الوصول */
.floating-btn:focus-visible {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* تحسينات للمس */
@media (hover: none) and (pointer: coarse) {
    .floating-btn {
        width: 110px;
        height: 55px;
    }

    .floating-btn:hover {
        width: 110px;
        transform: none;
    }

    .floating-btn:active {
        transform: scale(0.95);
    }
}

/* تحسينات للاتجاه من اليسار لليمين */
[dir="ltr"] .floating-buttons {
    left: 30px;
    right: auto;
}

[dir="ltr"] .floating-btn {
    flex-direction: row-reverse;
}

@media (max-width: 768px) {
    [dir="ltr"] .floating-buttons {
        left: 20px;
        right: auto;
    }
}

@media (max-width: 480px) {
    [dir="ltr"] .floating-buttons {
        left: 15px;
        right: auto;
    }
}

/* تحسينات للشاشات العريضة */
@media (min-width: 1200px) {
    .floating-buttons {
        right: 50px;
    }

    .floating-btn {
        width: 140px;
        height: 65px;
        font-size: 17px;
    }

    .floating-btn:hover {
        width: 150px;
    }

    .btn-label {
        font-size: 15px;
    }

    .floating-btn i {
        font-size: 20px;
    }
}

/* تحسينات للشاشات العريضة جداً */
@media (min-width: 1600px) {
    .floating-buttons {
        right: 80px;
        bottom: 120px;
    }
}
