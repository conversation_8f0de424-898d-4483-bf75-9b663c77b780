<?php
/**
 * سكريپت تطبيق التحديثات النهائية على جميع صفحات المدير
 */

// قائمة صفحات المدير
$admin_pages = [
    'admin_reports.php', 
    'admin_financial.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$updates_applied = [];
$errors = [];

echo "<h2>تطبيق التحديثات النهائية على صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إزالة أزرار التبديل المكررة
    $duplicate_theme_patterns = [
        '/<!-- زر تبديل الوضع الداكن\/الفاتح -->.*?<\/button>/s',
        '/function toggleTheme\(\).*?}/s',
        '/function loadTheme\(\).*?}/s'
    ];
    
    foreach ($duplicate_theme_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, '<!-- تم إزالة الكود المكرر -->', $content);
            $changes_made = true;
            echo "✅ تم إزالة كود مكرر<br>";
        }
    }
    
    // 2. تحديث البطاقات القديمة إلى النمط الجديد
    $old_card_patterns = [
        '/<div class="card border-left-[^"]*[^>]*>/' => '<div class="stats-card">',
        '/<div class="card[^>]*shadow[^>]*>/' => '<div class="modern-card">',
        '/<div class="card-header[^>]*>/' => '<div class="modern-card-header">',
        '/<div class="card-body[^>]*>/' => '<div class="modern-card-body">',
        '/<div class="text-xs[^>]*>/' => '<div class="stats-label">',
        '/<div class="h5[^>]*>/' => '<div class="stats-value">',
    ];
    
    foreach ($old_card_patterns as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
        }
    }
    
    // 3. تحديث رؤوس الصفحات
    $old_header_pattern = '/<div class="d-flex justify-content-between flex-wrap flex-md-nowrap[^>]*>.*?<\/div>/s';
    if (preg_match($old_header_pattern, $content)) {
        $page_title = getPageTitle($page);
        $page_icon = getPageIcon($page);
        $page_desc = getPageDescription($page);
        
        $new_header = generateModernHeader($page_title, $page_icon, $page_desc);
        $content = preg_replace($old_header_pattern, $new_header, $content);
        $changes_made = true;
        echo "✅ تم تحديث رأس الصفحة<br>";
    }
    
    // 4. إزالة fade-in من HTML
    $content = str_replace('fade-in', '', $content);
    
    // 5. تحديث الأزرار
    $content = preg_replace('/class="btn btn-([^"]*)"/', 'class="modern-btn modern-btn-$1"', $content);
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.final_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $updates_applied[] = $page;
            echo "✅ تم حفظ التحديثات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ التحديثات<br>";
        }
    } else {
        echo "ℹ️ لا توجد تحديثات مطلوبة<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>ملخص التحديثات النهائية:</h3>";

if (!empty($updates_applied)) {
    echo "<div style='color: green;'>";
    echo "<h4>✅ الملفات المحدثة بنجاح:</h4>";
    foreach ($updates_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<br><h3>التحديثات المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ إزالة أزرار التبديل المكررة</li>";
echo "<li>✅ تحديث البطاقات إلى النمط الجديد</li>";
echo "<li>✅ تحديث رؤوس الصفحات</li>";
echo "<li>✅ إزالة تأثيرات fade-in</li>";
echo "<li>✅ تحديث الأزرار</li>";
echo "<li>✅ تقليل الحركات والتأثيرات</li>";
echo "</ul>";

echo "<br><a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>العودة للوحة التحكم</a>";

/**
 * دوال مساعدة
 */
function getPageTitle($page) {
    $titles = [
        'admin_reports.php' => 'التقارير الشاملة',
        'admin_financial.php' => 'التقارير المالية',
        'admin_system.php' => 'إعدادات النظام',
        'admin_manage_admins.php' => 'إدارة المديرين',
        'admin_user_details.php' => 'تفاصيل المستخدم',
        'admin_invoice_details.php' => 'تفاصيل الفاتورة'
    ];
    return $titles[$page] ?? 'صفحة المدير';
}

function getPageIcon($page) {
    $icons = [
        'admin_reports.php' => 'fa-chart-bar',
        'admin_financial.php' => 'fa-file-invoice-dollar',
        'admin_system.php' => 'fa-cogs',
        'admin_manage_admins.php' => 'fa-user-shield',
        'admin_user_details.php' => 'fa-user',
        'admin_invoice_details.php' => 'fa-file-invoice'
    ];
    return $icons[$page] ?? 'fa-cog';
}

function getPageDescription($page) {
    $descriptions = [
        'admin_reports.php' => 'عرض وتحليل التقارير الشاملة للنظام',
        'admin_financial.php' => 'مراجعة التقارير المالية والإحصائيات',
        'admin_system.php' => 'إدارة وتكوين إعدادات النظام',
        'admin_manage_admins.php' => 'إدارة حسابات المديرين والصلاحيات',
        'admin_user_details.php' => 'عرض تفاصيل المستخدم الكاملة',
        'admin_invoice_details.php' => 'عرض تفاصيل الفاتورة الكاملة'
    ];
    return $descriptions[$page] ?? 'إدارة النظام';
}

function generateModernHeader($title, $icon, $description) {
    return '<!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas ' . $icon . ' me-3"></i>
                        ' . $title . '
                    </h1>
                    <p class="text-muted mb-0">' . $description . '</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>';
}
?>
