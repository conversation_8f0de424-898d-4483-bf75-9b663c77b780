<?php
/**
 * فحص الأخطاء التركيبية في ملف reports.php
 */

echo "<h1>🔍 فحص الأخطاء التركيبية</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f8f9fa; }
</style>";

echo "<div class='section'>";
echo "<h2>1. فحص الملف</h2>";

$file_path = __DIR__ . '/reports.php';
if (!file_exists($file_path)) {
    echo "<p class='error'>❌ ملف reports.php غير موجود</p>";
    exit;
}

$content = file_get_contents($file_path);
if ($content === false) {
    echo "<p class='error'>❌ فشل في قراءة الملف</p>";
    exit;
}

echo "<p class='info'>تم قراءة الملف بنجاح (" . number_format(strlen($content)) . " حرف)</p>";

$lines = explode("\n", $content);
echo "<p class='info'>عدد الأسطر: " . count($lines) . "</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. فحص الأخطاء التركيبية المحتملة</h2>";

$syntax_issues = [];

foreach ($lines as $line_num => $line) {
    $line_number = $line_num + 1;
    $trimmed_line = trim($line);
    
    // تخطي الأسطر الفارغة والتعليقات
    if (empty($trimmed_line) || strpos($trimmed_line, '//') === 0 || strpos($trimmed_line, '/*') === 0) {
        continue;
    }
    
    // فحص الأخطاء الشائعة
    $issues = [];
    
    // 1. فحص الأقواس المربعة غير المتطابقة
    $open_brackets = substr_count($line, '[');
    $close_brackets = substr_count($line, ']');
    if ($open_brackets != $close_brackets) {
        $issues[] = "أقواس مربعة غير متطابقة";
    }
    
    // 2. فحص الأقواس العادية غير المتطابقة
    $open_parens = substr_count($line, '(');
    $close_parens = substr_count($line, ')');
    if ($open_parens != $close_parens) {
        $issues[] = "أقواس عادية غير متطابقة";
    }
    
    // 3. فحص الأقواس المجعدة غير المتطابقة
    $open_braces = substr_count($line, '{');
    $close_braces = substr_count($line, '}');
    if ($open_braces != $close_braces) {
        $issues[] = "أقواس مجعدة غير متطابقة";
    }
    
    // 4. فحص علامات الاقتباس غير المتطابقة
    $single_quotes = substr_count($line, "'");
    $double_quotes = substr_count($line, '"');
    if ($single_quotes % 2 != 0) {
        $issues[] = "علامات اقتباس مفردة غير متطابقة";
    }
    if ($double_quotes % 2 != 0) {
        $issues[] = "علامات اقتباس مزدوجة غير متطابقة";
    }
    
    // 5. فحص استخدامات خاطئة للمتغيرات
    if (preg_match('/\$[a-zA-Z_][a-zA-Z0-9_]*\]es/', $line)) {
        $issues[] = "استخدام خاطئ للمتغير مع 'es'";
    }
    
    // 6. فحص دوال غير معرفة
    if (preg_match('/\bt\s*\(/', $line)) {
        $issues[] = "استخدام دالة t() غير معرفة";
    }
    
    // 7. فحص نقاط فاصلة مفقودة
    if (preg_match('/^\s*\$[^;]*[^;{}\s]$/', $trimmed_line) && !preg_match('/\?>\s*$/', $trimmed_line)) {
        $issues[] = "نقطة فاصلة مفقودة محتملة";
    }
    
    // 8. فحص أخطاء PHP tags
    if (preg_match('/<\?php.*\?>.*<\?php/', $line)) {
        $issues[] = "PHP tags متداخلة";
    }
    
    if (!empty($issues)) {
        $syntax_issues[] = [
            'line' => $line_number,
            'content' => $trimmed_line,
            'issues' => $issues
        ];
    }
}

if (empty($syntax_issues)) {
    echo "<p class='success'>✅ لم يتم العثور على أخطاء تركيبية واضحة</p>";
} else {
    echo "<p class='warning'>⚠️ تم العثور على " . count($syntax_issues) . " مشكلة محتملة</p>";
    
    echo "<table>";
    echo "<tr><th>السطر</th><th>المحتوى</th><th>المشاكل</th></tr>";
    
    foreach (array_slice($syntax_issues, 0, 20) as $issue) { // عرض أول 20 مشكلة فقط
        echo "<tr>";
        echo "<td>{$issue['line']}</td>";
        echo "<td><code>" . htmlspecialchars(substr($issue['content'], 0, 100)) . "...</code></td>";
        echo "<td>" . implode(', ', $issue['issues']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if (count($syntax_issues) > 20) {
        echo "<p class='info'>... و " . (count($syntax_issues) - 20) . " مشاكل أخرى</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. فحص تركيبي باستخدام PHP</h2>";

// محاولة فحص التركيب باستخدام php -l (إذا كان متاحاً)
$syntax_check_output = '';
$syntax_check_result = 0;

// تجربة فحص التركيب
ob_start();
$old_error_reporting = error_reporting(0);

try {
    // محاولة تضمين الملف للفحص
    include_once $file_path;
    echo "<p class='success'>✅ تم تحميل الملف بنجاح - لا توجد أخطاء تركيبية فادحة</p>";
} catch (ParseError $e) {
    echo "<p class='error'>❌ خطأ تركيبي: " . $e->getMessage() . "</p>";
    echo "<p class='error'>السطر: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p class='warning'>⚠️ خطأ في التنفيذ: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p class='warning'>⚠️ استثناء: " . $e->getMessage() . "</p>";
}

error_reporting($old_error_reporting);
ob_end_clean();

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار الصفحة</h2>";

echo "<h3>اختبار كشف الحساب:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب</a></li>";
echo "<li><a href='reports.php' target='_blank'>صفحة التقارير الرئيسية</a></li>";
echo "</ul>";

echo "<h3>أدوات الإصلاح:</h3>";
echo "<ul>";
echo "<li><a href='fix_syntax_errors.php'>إصلاح الأخطاء التركيبية</a></li>";
echo "<li><a href='clean_reports_file.php'>تنظيف الملف</a></li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. ملخص الفحص</h2>";

if (empty($syntax_issues)) {
    echo "<p class='success'>✅ الملف يبدو سليماً من الناحية التركيبية</p>";
    echo "<p class='info'>يمكنك الآن اختبار كشف الحساب</p>";
} else {
    echo "<p class='warning'>⚠️ توجد مشاكل تحتاج إلى مراجعة</p>";
    echo "<p class='info'>راجع المشاكل المذكورة أعلاه وقم بإصلاحها</p>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
