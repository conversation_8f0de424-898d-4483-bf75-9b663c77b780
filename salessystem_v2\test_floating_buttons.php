<?php
/**
 * اختبار الأزرار العائمة في جميع الصفحات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-rocket me-2"></i>اختبار الأزرار العائمة للفواتير السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الاختبار:</h6>
                        <ul class="mb-0">
                            <li>الأزرار العائمة يجب أن تظهر في الزاوية اليمنى السفلى من الصفحة</li>
                            <li>يجب أن تحتوي على زرين: "مبيعات" و "مشتريات"</li>
                            <li>الأزرار مفتوحة دائماً (لا تحتاج للنقر على زر رئيسي)</li>
                            <li>عند النقر على أي زر، يجب أن تفتح السلايد الجانبية للفاتورة السريعة</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6><i class="fas fa-file-invoice-dollar me-2"></i>اختبار زر المبيعات</h6>
                                </div>
                                <div class="card-body">
                                    <p>انقر على الزر الأخضر "مبيعات" في الأسفل لاختبار:</p>
                                    <ul>
                                        <li>فتح السلايد الجانبية</li>
                                        <li>عرض عنوان "فاتورة مبيعات سريعة"</li>
                                        <li>تحميل قائمة العملاء</li>
                                        <li>إضافة أصناف للفاتورة</li>
                                    </ul>
                                    <button type="button" class="btn btn-success" onclick="testSalesButton()">
                                        <i class="fas fa-play me-1"></i>
                                        اختبار زر المبيعات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6><i class="fas fa-shopping-cart me-2"></i>اختبار زر المشتريات</h6>
                                </div>
                                <div class="card-body">
                                    <p>انقر على الزر الأحمر "مشتريات" في الأسفل لاختبار:</p>
                                    <ul>
                                        <li>فتح السلايد الجانبية</li>
                                        <li>عرض عنوان "فاتورة مشتريات سريعة"</li>
                                        <li>تحميل قائمة الموردين</li>
                                        <li>إضافة أصناف للفاتورة</li>
                                    </ul>
                                    <button type="button" class="btn btn-danger" onclick="testPurchasesButton()">
                                        <i class="fas fa-play me-1"></i>
                                        اختبار زر المشتريات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6><i class="fas fa-list-check me-2"></i>قائمة فحص الوظائف</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>الأزرار العائمة:</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check1">
                                                <label class="form-check-label" for="check1">
                                                    الأزرار ظاهرة في الزاوية اليمنى السفلى
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check2">
                                                <label class="form-check-label" for="check2">
                                                    زر المبيعات (أخضر) موجود
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check3">
                                                <label class="form-check-label" for="check3">
                                                    زر المشتريات (أحمر) موجود
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check4">
                                                <label class="form-check-label" for="check4">
                                                    الأزرار تحتوي على نص وأيقونة
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>السلايد الجانبية:</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check5">
                                                <label class="form-check-label" for="check5">
                                                    السلايد تفتح عند النقر على الأزرار
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check6">
                                                <label class="form-check-label" for="check6">
                                                    العنوان يتغير حسب نوع الفاتورة
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check7">
                                                <label class="form-check-label" for="check7">
                                                    قائمة العملاء/الموردين تتحدث
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check8">
                                                <label class="form-check-label" for="check8">
                                                    يمكن إضافة أصناف للفاتورة
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6><i class="fas fa-link me-2"></i>اختبار في صفحات أخرى</h6>
                                </div>
                                <div class="card-body">
                                    <p>انتقل للصفحات التالية للتأكد من ظهور الأزرار العائمة:</p>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="index.php" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-home me-1"></i>
                                                الصفحة الرئيسية
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="sales.php" class="btn btn-outline-success w-100 mb-2">
                                                <i class="fas fa-file-invoice-dollar me-1"></i>
                                                المبيعات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="purchases.php" class="btn btn-outline-danger w-100 mb-2">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                المشتريات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="customers.php" class="btn btn-outline-info w-100 mb-2">
                                                <i class="fas fa-users me-1"></i>
                                                العملاء
                                            </a>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="products.php" class="btn btn-outline-secondary w-100 mb-2">
                                                <i class="fas fa-box me-1"></i>
                                                المنتجات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="reports.php" class="btn btn-outline-dark w-100 mb-2">
                                                <i class="fas fa-chart-bar me-1"></i>
                                                التقارير
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="settings.php" class="btn btn-outline-warning w-100 mb-2">
                                                <i class="fas fa-cog me-1"></i>
                                                الإعدادات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="profile.php" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                الملف الشخصي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6><i class="fas fa-bug me-2"></i>استكشاف الأخطاء</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>إذا لم تظهر الأزرار:</h6>
                                            <ul>
                                                <li>تحقق من تحميل ملف <code>floating-buttons.css</code></li>
                                                <li>تحقق من تحميل ملف <code>floating-buttons.js</code></li>
                                                <li>افتح أدوات المطور وتحقق من وجود أخطاء JavaScript</li>
                                                <li>تأكد من تسجيل الدخول</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>إذا لم تعمل السلايد الجانبية:</h6>
                                            <ul>
                                                <li>تحقق من تحميل ملف <code>quick-invoice.js</code></li>
                                                <li>تحقق من وجود ملف <code>quick-invoice-sidebar.php</code></li>
                                                <li>تأكد من عمل Bootstrap JavaScript</li>
                                                <li>تحقق من اتصال قاعدة البيانات</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-info" onclick="checkConsole()">
                                            <i class="fas fa-terminal me-1"></i>
                                            فحص وحدة التحكم
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="checkFiles()">
                                            <i class="fas fa-file-code me-1"></i>
                                            فحص الملفات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دوال الاختبار
function testSalesButton() {
    if (typeof openQuickInvoice === 'function') {
        openQuickInvoice('sale');
        alert('✅ تم استدعاء دالة فتح فاتورة المبيعات بنجاح!');
    } else {
        alert('❌ دالة openQuickInvoice غير موجودة. تحقق من تحميل floating-buttons.js');
    }
}

function testPurchasesButton() {
    if (typeof openQuickInvoice === 'function') {
        openQuickInvoice('purchase');
        alert('✅ تم استدعاء دالة فتح فاتورة المشتريات بنجاح!');
    } else {
        alert('❌ دالة openQuickInvoice غير موجودة. تحقق من تحميل floating-buttons.js');
    }
}

function checkConsole() {
    console.log('🔍 فحص وحدة التحكم:');
    console.log('- تحقق من وجود أخطاء JavaScript');
    console.log('- تحقق من تحميل الملفات المطلوبة');
    console.log('- تحقق من استجابة الخادم');
    
    // فحص الدوال المطلوبة
    const functions = [
        'openQuickInvoice',
        'closeQuickInvoice', 
        'addQuickItemRow',
        'updateQuickInvoiceSummary'
    ];
    
    functions.forEach(func => {
        if (typeof window[func] === 'function') {
            console.log(`✅ ${func}: موجودة`);
        } else {
            console.log(`❌ ${func}: غير موجودة`);
        }
    });
    
    // فحص العناصر المطلوبة
    const elements = [
        'quickInvoiceSidebar',
        'sidebarOverlay',
        'quickInvoiceForm'
    ];
    
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ ${elementId}: موجود`);
        } else {
            console.log(`❌ ${elementId}: غير موجود`);
        }
    });
    
    alert('تم فحص وحدة التحكم. افتح أدوات المطور (F12) لرؤية النتائج.');
}

function checkFiles() {
    const files = [
        'assets/css/floating-buttons.css',
        'assets/js/floating-buttons.js',
        'assets/js/quick-invoice.js',
        'includes/quick-invoice-sidebar.php'
    ];
    
    let results = '📁 فحص الملفات:\n\n';
    
    files.forEach(file => {
        // محاولة تحميل الملف للتحقق من وجوده
        fetch(file)
            .then(response => {
                if (response.ok) {
                    console.log(`✅ ${file}: موجود`);
                } else {
                    console.log(`❌ ${file}: غير موجود (${response.status})`);
                }
            })
            .catch(error => {
                console.log(`❌ ${file}: خطأ في التحميل`);
            });
    });
    
    alert('تم بدء فحص الملفات. افتح أدوات المطور (F12) لرؤية النتائج.');
}

// فحص تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء اختبار الأزرار العائمة');
    
    // التحقق من وجود الأزرار العائمة
    setTimeout(() => {
        const floatingButtons = document.querySelector('.floating-buttons');
        if (floatingButtons) {
            console.log('✅ الأزرار العائمة موجودة');
            
            const saleBtn = floatingButtons.querySelector('.sale-btn');
            const purchaseBtn = floatingButtons.querySelector('.purchase-btn');
            
            if (saleBtn) console.log('✅ زر المبيعات موجود');
            else console.log('❌ زر المبيعات غير موجود');
            
            if (purchaseBtn) console.log('✅ زر المشتريات موجود');
            else console.log('❌ زر المشتريات غير موجود');
            
        } else {
            console.log('❌ الأزرار العائمة غير موجودة');
        }
    }, 1000);
});
</script>

<?php require_once 'includes/footer.php'; ?>
