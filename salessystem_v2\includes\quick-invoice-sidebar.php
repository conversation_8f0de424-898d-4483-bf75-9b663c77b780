<?php
/**
 * السلايد الجانبية للفواتير السريعة
 * يتم تضمينها في جميع صفحات النظام
 */

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    return;
}

// جلب قاعدة البيانات
try {
    $db = getUnifiedDB();
    if (!$db) {
        return;
    }
} catch (Exception $e) {
    return;
}
?>

<!-- السلايد الجانبية للفاتورة السريعة -->
<div class="quick-invoice-sidebar" id="quickInvoiceSidebar">
    <div class="sidebar-header">
        <h5 id="sidebarTitle">فاتورة سريعة</h5>
        <button class="btn-close" onclick="closeQuickInvoice()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <form id="quickInvoiceForm" method="POST" action="process_quick_invoice.php">
            <input type="hidden" id="invoiceType" name="invoice_type" value="">
            
            <!-- معلومات العميل/المورد -->
            <div class="mb-3">
                <label for="quickCustomer" class="form-label" id="quickCustomerLabel">العميل/المورد</label>
                <select class="form-select" id="quickCustomer" name="customer_id" required>
                    <option value="">-- اختر --</option>
                    <option value="new">-- إضافة جديد --</option>
                </select>
            </div>

            <!-- تاريخ الفاتورة -->
            <div class="mb-3">
                <label for="quickDate" class="form-label">تاريخ الفاتورة</label>
                <input type="date" class="form-control" id="quickDate" name="date" value="<?php echo date('Y-m-d'); ?>" required>
            </div>

            <!-- جدول الأصناف -->
            <div class="mb-3">
                <label class="form-label">الأصناف</label>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered" id="quickItemsTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 35%;">المنتج</th>
                                <th style="width: 12%;">الكمية</th>
                                <th style="width: 15%;">السعر</th>
                                <th style="width: 12%;">ض%</th>
                                <th style="width: 18%;">المجموع</th>
                                <th style="width: 8%;">حذف</th>
                            </tr>
                        </thead>
                        <tbody id="quickItemsBody">
                            <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary w-100" id="addQuickItemBtn">
                    <i class="fas fa-plus me-1"></i>
                    إضافة صنف
                </button>
            </div>

            <!-- ملخص الفاتورة -->
            <div class="card mb-3">
                <div class="card-header">
                    <i class="fas fa-calculator me-1"></i>
                    ملخص الفاتورة
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <small class="text-muted">المجموع الفرعي:</small>
                        </div>
                        <div class="col-6 text-end">
                            <small><span id="quickSubtotalCell">0.00</span> ر.س</small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">الضريبة:</small>
                        </div>
                        <div class="col-6 text-end">
                            <small><span id="quickTaxCell">0.00</span> ر.س</small>
                        </div>
                        <div class="col-12"><hr class="my-2"></div>
                        <div class="col-6">
                            <strong class="text-primary">الإجمالي:</strong>
                        </div>
                        <div class="col-6 text-end">
                            <strong class="text-primary"><span id="quickTotalCell">0.00</span> ر.س</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="card mb-3">
                <div class="card-header">
                    <i class="fas fa-credit-card me-1"></i>
                    معلومات الدفع
                </div>
                <div class="card-body">
                    <div class="row g-2 mb-2">
                        <div class="col-6">
                            <label for="quickPaymentMethod" class="form-label">طريقة الدفع</label>
                            <select class="form-select form-select-sm" id="quickPaymentMethod" name="payment_method">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="installment">تقسيط</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label for="quickPaymentStatus" class="form-label">حالة الدفع</label>
                            <select class="form-select form-select-sm" id="quickPaymentStatus" name="payment_status" onchange="updateQuickPaymentFields()">
                                <option value="paid">مدفوع</option>
                                <option value="unpaid">غير مدفوع</option>
                                <option value="partial">مدفوع جزئياً</option>
                            </select>
                        </div>
                    </div>

                    <div class="row g-2">
                        <div class="col-6">
                            <label for="quickPaidAmount" class="form-label">المبلغ المدفوع</label>
                            <input type="number" class="form-control form-control-sm" id="quickPaidAmount" name="paid_amount" step="0.01" min="0" value="0" onchange="calculateQuickRemainingAmount()">
                        </div>
                        <div class="col-6">
                            <label for="quickPaymentDate" class="form-label">تاريخ الدفع</label>
                            <input type="date" class="form-control form-control-sm" id="quickPaymentDate" name="payment_date" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="mb-3">
                <label for="quickNotes" class="form-label">
                    <i class="fas fa-sticky-note me-1"></i>
                    ملاحظات
                </label>
                <textarea class="form-control form-control-sm" id="quickNotes" name="notes" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
            </div>

            <!-- أزرار الحفظ -->
            <div class="d-grid gap-2 mt-3">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save me-1"></i>
                    حفظ الفاتورة وطباعة POS
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="closeQuickInvoice()">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة إضافة عميل/مورد جديد -->
<div class="modal fade" id="quickAddCustomerModal" tabindex="-1" aria-labelledby="quickAddCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddCustomerForm">
                    <input type="hidden" id="quick_customer_type" value="customer">
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_name" class="form-label" id="quickCustomerNameLabel">اسم العميل *</label>
                        <input type="text" class="form-control" id="quick_new_customer_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="quick_new_customer_phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="quick_new_customer_email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_tax_number" class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-control" id="quick_new_customer_tax_number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="quick_new_customer_address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveQuickNewCustomer">حفظ</button>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط السلايد الجانبية */
.quick-invoice-sidebar {
    position: fixed;
    top: 0;
    right: -450px;
    width: 420px;
    height: 100vh;
    background: white;
    box-shadow: -3px 0 15px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transition: right 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #dee2e6;
}

.quick-invoice-sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    min-height: 60px;
}

.sidebar-header h5 {
    margin: 0;
    color: #495057;
    flex-grow: 1;
}

.sidebar-header .btn-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-header .btn-close:hover {
    background: #e9ecef;
    color: #495057;
}

.sidebar-content {
    padding: 15px;
    flex-grow: 1;
    overflow-y: auto;
    background: #fafbfc;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .quick-invoice-sidebar {
        width: 100%;
        right: -100%;
    }

    .sidebar-content {
        padding: 10px;
    }

    .quick-invoice-sidebar .form-control,
    .quick-invoice-sidebar .form-select {
        font-size: 14px;
        min-height: 38px;
    }

    #quickItemsTable {
        font-size: 12px;
    }

    #quickItemsTable th,
    #quickItemsTable td {
        padding: 6px 4px;
    }

    .quick-invoice-sidebar .btn {
        font-size: 14px;
        min-height: 40px;
    }
}

@media (max-width: 480px) {
    .sidebar-content {
        padding: 8px;
    }

    .quick-invoice-sidebar .card-body {
        padding: 8px;
    }

    #quickItemsTable .form-control,
    #quickItemsTable .form-select {
        font-size: 11px;
        min-height: 30px;
    }
}

/* تحسينات الجدول */
#quickItemsTable {
    font-size: 11px;
    margin-bottom: 10px;
}

#quickItemsTable th {
    padding: 6px 3px;
    vertical-align: middle;
    background: #e9ecef;
    font-weight: 600;
    font-size: 10px;
    text-align: center;
    border: 1px solid #dee2e6;
}

#quickItemsTable td {
    padding: 4px 2px;
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

#quickItemsTable .form-control,
#quickItemsTable .form-select {
    font-size: 10px;
    padding: 2px 4px;
    min-height: 28px;
    border: 1px solid #ced4da;
    border-radius: 3px;
}

#quickItemsTable .btn {
    padding: 2px 6px;
    font-size: 10px;
    min-height: 28px;
}

/* تحسينات النماذج */
.quick-invoice-sidebar .form-control,
.quick-invoice-sidebar .form-select {
    font-size: 12px;
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #ced4da;
    min-height: 35px;
}

.quick-invoice-sidebar .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    font-size: 12px;
}

.quick-invoice-sidebar .mb-3 {
    margin-bottom: 12px !important;
}

.quick-invoice-sidebar .row {
    margin-left: -5px;
    margin-right: -5px;
}

.quick-invoice-sidebar .row > * {
    padding-left: 5px;
    padding-right: 5px;
}

/* تحسينات البطاقات */
.quick-invoice-sidebar .card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-invoice-sidebar .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 12px;
    padding: 8px 12px;
    color: #495057;
}

.quick-invoice-sidebar .card-body {
    padding: 10px 12px;
}

.quick-invoice-sidebar .card-body .table {
    margin-bottom: 0;
    font-size: 11px;
}

.quick-invoice-sidebar .card-body .table th,
.quick-invoice-sidebar .card-body .table td {
    padding: 4px 8px;
    border-top: 1px solid #dee2e6;
}

.quick-invoice-sidebar .card-body .table th {
    font-weight: 600;
    background: #f8f9fa;
}

/* تحسينات الأزرار */
.quick-invoice-sidebar .btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
    min-height: 35px;
}

.quick-invoice-sidebar .btn-sm {
    font-size: 10px;
    padding: 4px 8px;
    min-height: 28px;
}

.quick-invoice-sidebar .d-grid {
    gap: 8px;
}

/* تحسينات إضافية للمساحات */
.quick-invoice-sidebar textarea {
    resize: vertical;
    min-height: 60px;
}

.quick-invoice-sidebar .table-responsive {
    border-radius: 4px;
    border: 1px solid #dee2e6;
    max-height: 200px;
    overflow-y: auto;
}

.quick-invoice-sidebar .alert {
    padding: 8px 12px;
    font-size: 11px;
    margin-bottom: 10px;
}
</style>

<script>
// تحميل بيانات المنتجات للفواتير السريعة
const quickProducts = [
    <?php
    try {
        // التحقق من وجود عمود is_active
        $columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
        $has_is_active = $columns_check && $columns_check->num_rows > 0;

        if ($has_is_active) {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products WHERE is_active = 1 ORDER BY category, name");
        } else {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
        }

        if ($products_result && $products_result->num_rows > 0) {
            while ($product = $products_result->fetch_assoc()) {
                $category = $product['category'] ? addslashes($product['category']) : '';
                echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}, category: '$category'},";
            }
        }
    } catch (Exception $e) {
        error_log("Error loading products: " . $e->getMessage());
    }
    ?>
];
</script>
