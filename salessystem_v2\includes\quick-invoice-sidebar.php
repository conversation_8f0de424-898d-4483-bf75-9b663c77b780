<?php
/**
 * السلايد الجانبية للفواتير السريعة
 * يتم تضمينها في جميع صفحات النظام
 */

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    return;
}

// جلب قاعدة البيانات
try {
    $db = getUnifiedDB();
    if (!$db) {
        return;
    }
} catch (Exception $e) {
    return;
}
?>

<!-- السلايد الجانبية للفاتورة السريعة -->
<div class="quick-invoice-sidebar" id="quickInvoiceSidebar">
    <div class="sidebar-header">
        <h5 id="sidebarTitle">فاتورة سريعة</h5>
        <button class="btn-close" onclick="closeQuickInvoice()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <form id="quickInvoiceForm" method="POST" action="process_quick_invoice.php">
            <input type="hidden" id="invoiceType" name="invoice_type" value="">
            
            <!-- معلومات العميل/المورد -->
            <div class="mb-3">
                <label for="quickCustomer" class="form-label" id="quickCustomerLabel">العميل/المورد</label>
                <select class="form-select" id="quickCustomer" name="customer_id" required>
                    <option value="">-- اختر --</option>
                    <option value="new">-- إضافة جديد --</option>
                </select>
            </div>

            <!-- تاريخ الفاتورة -->
            <div class="mb-3">
                <label for="quickDate" class="form-label">تاريخ الفاتورة</label>
                <input type="date" class="form-control" id="quickDate" name="date" value="<?php echo date('Y-m-d'); ?>" required>
            </div>

            <!-- جدول الأصناف -->
            <div class="mb-3">
                <label class="form-label">الأصناف</label>
                <div class="table-responsive">
                    <table class="table table-sm" id="quickItemsTable">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الضريبة%</th>
                                <th>المجموع</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="quickItemsBody">
                            <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary" id="addQuickItemBtn">
                    <i class="fas fa-plus me-1"></i>
                    إضافة صنف
                </button>
            </div>

            <!-- ملخص الفاتورة -->
            <div class="card mb-3">
                <div class="card-header">ملخص الفاتورة</div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>المجموع الفرعي:</th>
                            <td class="text-end"><span id="quickSubtotalCell">0.00</span> ر.س</td>
                        </tr>
                        <tr>
                            <th>الضريبة:</th>
                            <td class="text-end"><span id="quickTaxCell">0.00</span> ر.س</td>
                        </tr>
                        <tr class="table-primary">
                            <th>الإجمالي:</th>
                            <td class="text-end"><strong><span id="quickTotalCell">0.00</span> ر.س</strong></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="quickPaymentMethod" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="quickPaymentMethod" name="payment_method">
                        <option value="cash">نقدي</option>
                        <option value="card">بطاقة</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                        <option value="installment">تقسيط</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="quickPaymentStatus" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="quickPaymentStatus" name="payment_status" onchange="updateQuickPaymentFields()">
                        <option value="paid">مدفوع</option>
                        <option value="unpaid">غير مدفوع</option>
                        <option value="partial">مدفوع جزئياً</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="quickPaidAmount" class="form-label">المبلغ المدفوع</label>
                    <input type="number" class="form-control" id="quickPaidAmount" name="paid_amount" step="0.01" min="0" value="0" onchange="calculateQuickRemainingAmount()">
                </div>
                <div class="col-md-6">
                    <label for="quickPaymentDate" class="form-label">تاريخ الدفع</label>
                    <input type="date" class="form-control" id="quickPaymentDate" name="payment_date" value="<?php echo date('Y-m-d'); ?>">
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="mb-3">
                <label for="quickNotes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="quickNotes" name="notes" rows="2"></textarea>
            </div>

            <!-- أزرار الحفظ -->
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ الفاتورة
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeQuickInvoice()">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة إضافة عميل/مورد جديد -->
<div class="modal fade" id="quickAddCustomerModal" tabindex="-1" aria-labelledby="quickAddCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddCustomerForm">
                    <input type="hidden" id="quick_customer_type" value="customer">
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_name" class="form-label" id="quickCustomerNameLabel">اسم العميل *</label>
                        <input type="text" class="form-control" id="quick_new_customer_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="quick_new_customer_phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="quick_new_customer_email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_tax_number" class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-control" id="quick_new_customer_tax_number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_new_customer_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="quick_new_customer_address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveQuickNewCustomer">حفظ</button>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط السلايد الجانبية */
.quick-invoice-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    transition: right 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.quick-invoice-sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-shrink: 0;
}

.sidebar-header h5 {
    margin: 0;
    color: #495057;
    flex-grow: 1;
}

.sidebar-header .btn-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-header .btn-close:hover {
    background: #e9ecef;
    color: #495057;
}

.sidebar-content {
    padding: 20px;
    flex-grow: 1;
    overflow-y: auto;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .quick-invoice-sidebar {
        width: 100%;
        right: -100%;
    }
}

/* تحسينات الجدول */
#quickItemsTable {
    font-size: 12px;
}

#quickItemsTable th,
#quickItemsTable td {
    padding: 8px 4px;
    vertical-align: middle;
}

#quickItemsTable .form-control,
#quickItemsTable .form-select {
    font-size: 12px;
    padding: 4px 8px;
    min-height: 32px;
}

/* تحسينات النماذج */
.quick-invoice-sidebar .form-control,
.quick-invoice-sidebar .form-select {
    font-size: 14px;
    padding: 8px 12px;
}

.quick-invoice-sidebar .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 13px;
}

/* تحسينات البطاقات */
.quick-invoice-sidebar .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.quick-invoice-sidebar .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 14px;
    padding: 10px 15px;
}

.quick-invoice-sidebar .card-body {
    padding: 15px;
}

/* تحسينات الأزرار */
.quick-invoice-sidebar .btn {
    font-size: 14px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
}
</style>

<script>
// تحميل بيانات المنتجات للفواتير السريعة
const quickProducts = [
    <?php
    try {
        // التحقق من وجود عمود is_active
        $columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
        $has_is_active = $columns_check && $columns_check->num_rows > 0;

        if ($has_is_active) {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products WHERE is_active = 1 ORDER BY category, name");
        } else {
            $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
        }

        if ($products_result && $products_result->num_rows > 0) {
            while ($product = $products_result->fetch_assoc()) {
                $category = $product['category'] ? addslashes($product['category']) : '';
                echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}, category: '$category'},";
            }
        }
    } catch (Exception $e) {
        error_log("Error loading products: " . $e->getMessage());
    }
    ?>
];
</script>
