<?php
/**
 * اختبار نظام طباعة POS
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header("Location: login.php");
    exit();
}

echo "<h2>🧪 اختبار نظام طباعة POS</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // جلب عينة من الفواتير للاختبار
    $sales_stmt = $db->prepare("SELECT id, invoice_number, total_amount, invoice_date FROM sales WHERE user_id = ? ORDER BY id DESC LIMIT 5");
    $sales_stmt->bind_param("i", $_SESSION['user_id']);
    $sales_stmt->execute();
    $sales_result = $sales_stmt->get_result();
    $sales = $sales_result->fetch_all(MYSQLI_ASSOC);
    $sales_stmt->close();

    $purchases_stmt = $db->prepare("SELECT id, invoice_number, total_amount, invoice_date FROM purchases WHERE user_id = ? ORDER BY id DESC LIMIT 5");
    $purchases_stmt->bind_param("i", $_SESSION['user_id']);
    $purchases_stmt->execute();
    $purchases_result = $purchases_stmt->get_result();
    $purchases = $purchases_result->fetch_all(MYSQLI_ASSOC);
    $purchases_stmt->close();

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            margin: 5px;
            min-width: 120px;
        }
        .invoice-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- معلومات النظام -->
        <div class="test-section">
            <h4><i class="fas fa-info-circle text-primary me-2"></i>معلومات نظام طباعة POS</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الإصدار:</strong> 1.0</p>
                    <p><strong>أحجام الورق المدعومة:</strong> 58mm, 80mm</p>
                    <p><strong>أنواع الفواتير:</strong> مبيعات، مشتريات</p>
                </div>
                <div class="col-md-6">
                    <p><strong>المميزات:</strong> طباعة تلقائية، معاينة، خيارات متقدمة</p>
                    <p><strong>التوافق:</strong> طابعات حرارية وعادية</p>
                    <p><strong>المتصفحات:</strong> Chrome, Firefox, Safari, Edge</p>
                </div>
            </div>
        </div>

        <!-- اختبار الملفات -->
        <div class="test-section">
            <h4><i class="fas fa-file-check text-success me-2"></i>فحص الملفات المطلوبة</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="invoice-item">
                        <span class="status-indicator <?php echo file_exists('assets/css/pos-print.css') ? 'status-success' : 'status-error'; ?>"></span>
                        <strong>pos-print.css</strong><br>
                        <small><?php echo file_exists('assets/css/pos-print.css') ? '✅ موجود' : '❌ مفقود'; ?></small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="invoice-item">
                        <span class="status-indicator <?php echo file_exists('assets/js/pos-print.js') ? 'status-success' : 'status-error'; ?>"></span>
                        <strong>pos-print.js</strong><br>
                        <small><?php echo file_exists('assets/js/pos-print.js') ? '✅ موجود' : '❌ مفقود'; ?></small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="invoice-item">
                        <span class="status-indicator <?php echo file_exists('print_pos_invoice.php') ? 'status-success' : 'status-error'; ?>"></span>
                        <strong>print_pos_invoice.php</strong><br>
                        <small><?php echo file_exists('print_pos_invoice.php') ? '✅ موجود' : '❌ مفقود'; ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار فواتير المبيعات -->
        <?php if (!empty($sales)): ?>
        <div class="test-section">
            <h4><i class="fas fa-shopping-cart text-success me-2"></i>اختبار فواتير المبيعات</h4>
            <div class="row">
                <?php foreach ($sales as $sale): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="invoice-item">
                        <h6>فاتورة رقم: <?php echo htmlspecialchars($sale['invoice_number']); ?></h6>
                        <p class="mb-2">
                            <small>
                                <strong>المبلغ:</strong> <?php echo number_format($sale['total_amount'], 2); ?> ر.س<br>
                                <strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($sale['invoice_date'])); ?>
                            </small>
                        </p>
                        <div class="btn-group-vertical w-100">
                            <button class="btn btn-sm btn-info test-button" 
                                    onclick="testPOSPrint(<?php echo $sale['id']; ?>, 'sale', '58')">
                                <i class="fas fa-receipt me-1"></i>طباعة 58mm
                            </button>
                            <button class="btn btn-sm btn-primary test-button" 
                                    onclick="testPOSPrint(<?php echo $sale['id']; ?>, 'sale', '80')">
                                <i class="fas fa-receipt me-1"></i>طباعة 80mm
                            </button>
                            <button class="btn btn-sm btn-success test-button" 
                                    onclick="printPOSInvoice(<?php echo $sale['id']; ?>, 'sale')">
                                <i class="fas fa-cog me-1"></i>خيارات متقدمة
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- اختبار فواتير المشتريات -->
        <?php if (!empty($purchases)): ?>
        <div class="test-section">
            <h4><i class="fas fa-shopping-bag text-danger me-2"></i>اختبار فواتير المشتريات</h4>
            <div class="row">
                <?php foreach ($purchases as $purchase): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="invoice-item">
                        <h6>فاتورة رقم: <?php echo htmlspecialchars($purchase['invoice_number']); ?></h6>
                        <p class="mb-2">
                            <small>
                                <strong>المبلغ:</strong> <?php echo number_format($purchase['total_amount'], 2); ?> ر.س<br>
                                <strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($purchase['invoice_date'])); ?>
                            </small>
                        </p>
                        <div class="btn-group-vertical w-100">
                            <button class="btn btn-sm btn-info test-button" 
                                    onclick="testPOSPrint(<?php echo $purchase['id']; ?>, 'purchase', '58')">
                                <i class="fas fa-receipt me-1"></i>طباعة 58mm
                            </button>
                            <button class="btn btn-sm btn-primary test-button" 
                                    onclick="testPOSPrint(<?php echo $purchase['id']; ?>, 'purchase', '80')">
                                <i class="fas fa-receipt me-1"></i>طباعة 80mm
                            </button>
                            <button class="btn btn-sm btn-success test-button" 
                                    onclick="printPOSInvoice(<?php echo $purchase['id']; ?>, 'purchase')">
                                <i class="fas fa-cog me-1"></i>خيارات متقدمة
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- اختبارات متقدمة -->
        <div class="test-section">
            <h4><i class="fas fa-tools text-warning me-2"></i>اختبارات متقدمة</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>اختبار الطباعة المتعددة</h6>
                    <button class="btn btn-warning test-button" onclick="testBulkPrint()">
                        <i class="fas fa-layer-group me-1"></i>طباعة متعددة
                    </button>
                    <button class="btn btn-info test-button" onclick="testQuickPrint()">
                        <i class="fas fa-bolt me-1"></i>طباعة سريعة
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>اختبار الأخطاء</h6>
                    <button class="btn btn-secondary test-button" onclick="testInvalidInvoice()">
                        <i class="fas fa-exclamation-triangle me-1"></i>فاتورة غير موجودة
                    </button>
                    <button class="btn btn-dark test-button" onclick="testNetworkError()">
                        <i class="fas fa-wifi me-1"></i>خطأ شبكة
                    </button>
                </div>
            </div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h4><i class="fas fa-chart-line text-info me-2"></i>نتائج الاختبار</h4>
            <div id="testResults">
                <p class="text-muted">لم يتم تشغيل أي اختبارات بعد...</p>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="text-center mt-4">
            <a href="sales.php" class="btn btn-success">
                <i class="fas fa-shopping-cart me-1"></i>المبيعات
            </a>
            <a href="purchases.php" class="btn btn-danger">
                <i class="fas fa-shopping-bag me-1"></i>المشتريات
            </a>
            <a href="dashboard.php" class="btn btn-primary">
                <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
            </a>
        </div>
    </div>

    <script src="assets/js/pos-print.js"></script>
    <script>
        let testCount = 0;
        let successCount = 0;
        let errorCount = 0;

        // اختبار طباعة POS مع حجم محدد
        function testPOSPrint(invoiceId, type, width) {
            testCount++;
            updateTestResults(`🧪 اختبار ${testCount}: طباعة ${type} رقم ${invoiceId} بحجم ${width}mm`);
            
            try {
                const url = `print_pos_invoice.php?id=${invoiceId}&type=${type}&width=${width}`;
                const printWindow = window.open(url, 'POSTest', 'width=400,height=600,scrollbars=yes');
                
                if (printWindow) {
                    successCount++;
                    updateTestResults(`✅ نجح: فتح نافذة الطباعة`);
                } else {
                    errorCount++;
                    updateTestResults(`❌ فشل: لم يتم فتح نافذة الطباعة`);
                }
            } catch (error) {
                errorCount++;
                updateTestResults(`❌ خطأ: ${error.message}`);
            }
        }

        // اختبار الطباعة المتعددة
        function testBulkPrint() {
            const invoices = [<?php echo !empty($sales) ? implode(',', array_column($sales, 'id')) : ''; ?>];
            if (invoices.length > 0) {
                bulkPOSPrint(invoices, 'sale');
                updateTestResults(`🔄 اختبار الطباعة المتعددة: ${invoices.length} فاتورة`);
            } else {
                updateTestResults(`⚠️ لا توجد فواتير للاختبار`);
            }
        }

        // اختبار الطباعة السريعة
        function testQuickPrint() {
            <?php if (!empty($sales)): ?>
            quickPOSPrint(<?php echo $sales[0]['id']; ?>, 'sale');
            updateTestResults(`⚡ اختبار الطباعة السريعة: فاتورة رقم <?php echo $sales[0]['id']; ?>`);
            <?php else: ?>
            updateTestResults(`⚠️ لا توجد فواتير للاختبار`);
            <?php endif; ?>
        }

        // اختبار فاتورة غير موجودة
        function testInvalidInvoice() {
            testCount++;
            updateTestResults(`🧪 اختبار ${testCount}: فاتورة غير موجودة`);
            
            try {
                printPOSInvoice(99999, 'sale');
                updateTestResults(`⚠️ تم تشغيل الاختبار - تحقق من رسالة الخطأ`);
            } catch (error) {
                updateTestResults(`✅ تم اكتشاف الخطأ بنجاح: ${error.message}`);
            }
        }

        // اختبار خطأ الشبكة
        function testNetworkError() {
            testCount++;
            updateTestResults(`🧪 اختبار ${testCount}: خطأ شبكة`);
            
            const url = `print_pos_invoice.php?id=invalid&type=invalid`;
            const printWindow = window.open(url, 'NetworkTest', 'width=400,height=600');
            updateTestResults(`🌐 تم فتح رابط خاطئ للاختبار`);
        }

        // تحديث نتائج الاختبار
        function updateTestResults(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            
            if (testCount === 1) {
                resultsDiv.innerHTML = ''; // مسح الرسالة الأولية
            }
            
            resultsDiv.innerHTML += `
                <div class="alert alert-info alert-sm mb-2">
                    <small><strong>[${timestamp}]</strong> ${message}</small>
                </div>
            `;
            
            // تحديث الإحصائيات
            if (testCount > 0) {
                resultsDiv.innerHTML += `
                    <div class="mt-3 p-2 bg-light rounded">
                        <small>
                            <strong>إحصائيات:</strong> 
                            المجموع: ${testCount} | 
                            النجاح: <span class="text-success">${successCount}</span> | 
                            الأخطاء: <span class="text-danger">${errorCount}</span>
                        </small>
                    </div>
                `;
            }
            
            // التمرير للأسفل
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            updateTestResults('🚀 نظام اختبار طباعة POS جاهز للاستخدام');
        });
    </script>
</body>
</html>
