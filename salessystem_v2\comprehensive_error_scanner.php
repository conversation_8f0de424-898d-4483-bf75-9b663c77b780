<?php
/**
 * فحص شامل للأخطاء في جميع ملفات PHP
 */

// قائمة جميع ملفات PHP في النظام
$php_files = [
    // ملفات المدير
    'admin_dashboard.php',
    'admin_users.php',
    'admin_activity.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php',
    'admin_login.php',
    'admin_logout.php',
    
    // ملفات النظام الرئيسية
    'index.php',
    'login.php',
    'logout.php',
    'register.php',
    'profile.php',
    'settings.php',
    'reports.php',
    
    // ملفات العملاء والمنتجات
    'customers.php',
    'products.php',
    'add_customer.php',
    'edit_customer.php',
    'delete_customer.php',
    'save_customer.php',
    'get_customer.php',
    'get_customers_by_type.php',
    'save_product.php',
    'get_product.php',
    'get_products.php',
    
    // ملفات المبيعات والمشتريات
    'sales.php',
    'purchases.php',
    'add_sale.php',
    'add_purchase.php',
    'edit_sale.php',
    'edit_purchase.php',
    'delete_sale.php',
    'delete_purchase.php',
    'view_sale.php',
    'view_purchase.php',
    
    // ملفات الفواتير والتقارير
    'invoice_preview.php',
    'print_invoice.php',
    'print_report.php',
    'print_report_handler.php',
    'process_quick_invoice.php',
    'account_statement.php',
    'tax_calculator.php',
    
    // ملفات أخرى
    'ajax_handler.php',
    'forgot_password.php',
    'reset_password.php',
    'test_invoice.php',
    'test_save_settings.php'
];

$total_files = 0;
$files_with_errors = 0;
$total_errors = 0;
$error_report = [];

echo "<h2>🔍 فحص شامل للأخطاء في جميع ملفات PHP</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>📋 الفحوصات المطبقة:</h4>";
echo "<ul>";
echo "<li>✅ فحص أخطاء PHP Syntax</li>";
echo "<li>✅ فحص الأقواس المتطابقة</li>";
echo "<li>✅ فحص تنسيق الكود</li>";
echo "<li>✅ فحص الدوال المفقودة</li>";
echo "<li>✅ فحص المتغيرات غير المعرفة</li>";
echo "<li>✅ فحص استعلامات SQL</li>";
echo "<li>✅ فحص JavaScript</li>";
echo "</ul>";
echo "</div>";

foreach ($php_files as $file) {
    $file_path = __DIR__ . '/' . $file;
    $file_errors = [];
    
    echo "<h3>🔍 فحص: $file</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if (!file_exists($file_path)) {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span><br>";
        echo "</div>";
        continue;
    }
    
    $total_files++;
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $file_errors[] = "فشل في قراءة الملف";
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    // 1. فحص PHP Syntax
    $syntax_check = shell_exec("php -l \"$file_path\" 2>&1");
    if (strpos($syntax_check, 'No syntax errors') === false) {
        $file_errors[] = "خطأ في PHP Syntax: " . trim($syntax_check);
        echo "<span style='color: red;'>❌ خطأ في PHP Syntax</span><br>";
    } else {
        echo "<span style='color: green;'>✅ PHP Syntax صحيح</span><br>";
    }
    
    // 2. فحص الأقواس المتطابقة
    $open_braces = substr_count($content, '{');
    $close_braces = substr_count($content, '}');
    $open_brackets = substr_count($content, '[');
    $close_brackets = substr_count($content, ']');
    $open_parens = substr_count($content, '(');
    $close_parens = substr_count($content, ')');
    
    if ($open_braces !== $close_braces) {
        $file_errors[] = "أقواس متعرجة غير متطابقة ({: $open_braces, }: $close_braces)";
        echo "<span style='color: red;'>❌ أقواس متعرجة غير متطابقة</span><br>";
    } else {
        echo "<span style='color: green;'>✅ أقواس متعرجة متطابقة</span><br>";
    }
    
    if ($open_brackets !== $close_brackets) {
        $file_errors[] = "أقواس مربعة غير متطابقة ([: $open_brackets, ]: $close_brackets)";
        echo "<span style='color: red;'>❌ أقواس مربعة غير متطابقة</span><br>";
    } else {
        echo "<span style='color: green;'>✅ أقواس مربعة متطابقة</span><br>";
    }
    
    if ($open_parens !== $close_parens) {
        $file_errors[] = "أقواس عادية غير متطابقة ((: $open_parens, ): $close_parens)";
        echo "<span style='color: red;'>❌ أقواس عادية غير متطابقة</span><br>";
    } else {
        echo "<span style='color: green;'>✅ أقواس عادية متطابقة</span><br>";
    }
    
    // 3. فحص تنسيق الكود
    $line_count = substr_count($content, "\n");
    if ($line_count < 10) {
        $file_errors[] = "الملف مضغوط في أسطر قليلة ($line_count أسطر)";
        echo "<span style='color: orange;'>⚠️ الملف مضغوط ($line_count أسطر)</span><br>";
    } else {
        echo "<span style='color: green;'>✅ تنسيق الكود جيد ($line_count أسطر)</span><br>";
    }
    
    // 4. فحص الدوال المفقودة الشائعة
    $common_functions = [
        'displayMessages' => 'includes/functions.php',
        'isAdminLoggedIn' => 'includes/auth.php',
        'hasAdminPermission' => 'includes/auth.php',
        'getUnifiedDB' => 'config/unified_db_config.php',
        'ErrorHandler::' => 'includes/error_handler.php'
    ];
    
    foreach ($common_functions as $func => $required_file) {
        if (strpos($content, $func) !== false) {
            if (strpos($content, "require_once") !== false && strpos($content, $required_file) !== false) {
                echo "<span style='color: green;'>✅ دالة $func مع الملف المطلوب</span><br>";
            } else {
                $file_errors[] = "دالة $func مستخدمة لكن $required_file غير مضمن";
                echo "<span style='color: orange;'>⚠️ دالة $func بدون تضمين $required_file</span><br>";
            }
        }
    }
    
    // 5. فحص مشاكل SQL شائعة
    if (preg_match('/\$db->query\([^)]*\$[^)]*\)/', $content)) {
        if (strpos($content, 'prepare') === false) {
            $file_errors[] = "استعلامات SQL غير آمنة (بدون prepared statements)";
            echo "<span style='color: orange;'>⚠️ استعلامات SQL غير آمنة</span><br>";
        }
    }
    
    // 6. فحص JavaScript المكسور
    if (strpos($content, '<script>') !== false) {
        // فحص أخطاء JavaScript شائعة
        if (preg_match('/function\s+\w+\s*\(\s*\)\s*\{[^}]*function/', $content)) {
            $file_errors[] = "دوال JavaScript متداخلة بشكل خاطئ";
            echo "<span style='color: red;'>❌ دوال JavaScript متداخلة خطأ</span><br>";
        } else {
            echo "<span style='color: green;'>✅ JavaScript يبدو صحيحاً</span><br>";
        }
    }
    
    // 7. فحص مشاكل CSS
    if (strpos($content, '<style>') !== false) {
        // فحص CSS فارغ أو مكسور
        if (preg_match('/\.\s*\{[^}]*\}/', $content)) {
            $file_errors[] = "CSS classes فارغة أو مكسورة";
            echo "<span style='color: orange;'>⚠️ CSS classes فارغة</span><br>";
        } else {
            echo "<span style='color: green;'>✅ CSS يبدو صحيحاً</span><br>";
        }
    }
    
    // 8. فحص مشاكل الترميز
    if (!mb_check_encoding($content, 'UTF-8')) {
        $file_errors[] = "مشكلة في ترميز الملف (ليس UTF-8)";
        echo "<span style='color: red;'>❌ مشكلة في الترميز</span><br>";
    } else {
        echo "<span style='color: green;'>✅ ترميز UTF-8 صحيح</span><br>";
    }
    
    // تسجيل النتائج
    if (!empty($file_errors)) {
        $files_with_errors++;
        $total_errors += count($file_errors);
        $error_report[$file] = $file_errors;
        echo "<div style='color: red; font-weight: bold; margin-top: 10px;'>";
        echo "❌ عدد الأخطاء: " . count($file_errors);
        echo "</div>";
    } else {
        echo "<div style='color: green; font-weight: bold; margin-top: 10px;'>";
        echo "🎉 الملف سليم تماماً!";
        echo "</div>";
    }
    
    echo "</div>";
}

// عرض الملخص النهائي
echo "<h3>📊 ملخص الفحص الشامل</h3>";

echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";

// بطاقة الملفات المفحوصة
echo "<div style='flex: 1; background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h4 style='color: #1976d2; margin: 0;'>📁 ملفات مفحوصة</h4>";
echo "<p style='color: #1976d2; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$total_files</p>";
echo "</div>";

// بطاقة الملفات مع أخطاء
echo "<div style='flex: 1; background: #ffebee; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h4 style='color: #d32f2f; margin: 0;'>❌ ملفات بها أخطاء</h4>";
echo "<p style='color: #d32f2f; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$files_with_errors</p>";
echo "</div>";

// بطاقة إجمالي الأخطاء
echo "<div style='flex: 1; background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h4 style='color: #f57c00; margin: 0;'>⚠️ إجمالي الأخطاء</h4>";
echo "<p style='color: #f57c00; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$total_errors</p>";
echo "</div>";

// بطاقة معدل النجاح
$success_rate = $total_files > 0 ? round((($total_files - $files_with_errors) / $total_files) * 100, 1) : 0;
echo "<div style='flex: 1; background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h4 style='color: #2e7d32; margin: 0;'>✅ معدل النجاح</h4>";
echo "<p style='color: #2e7d32; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$success_rate%</p>";
echo "</div>";

echo "</div>";

// عرض تفاصيل الأخطاء
if (!empty($error_report)) {
    echo "<div style='background: #ffebee; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #d32f2f;'>🔧 تفاصيل الأخطاء التي تحتاج إصلاح:</h4>";
    foreach ($error_report as $file => $errors) {
        echo "<h5 style='color: #d32f2f;'>$file:</h5>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: #d32f2f;'>$error</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

// روابط الإصلاح
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🛠️ أدوات الإصلاح:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";
echo "<a href='fix_formatting_issues.php' style='background: #2196f3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 8px;'>🔧 إصلاح التنسيق</a>";
echo "<a href='fix_css_issues.php' style='background: #4caf50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 8px;'>🎨 إصلاح CSS</a>";
echo "<a href='final_check.php' style='background: #ff9800; color: white; padding: 12px 20px; text-decoration: none; border-radius: 8px;'>✅ فحص نهائي</a>";
echo "</div>";
echo "</div>";

// رسالة النتيجة النهائية
if ($files_with_errors === 0) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تهانينا! جميع الملفات سليمة!</h3>";
    echo "<p>لا توجد أخطاء في أي من ملفات PHP المفحوصة.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>⚠️ هناك أخطاء تحتاج إصلاح</h3>";
    echo "<p>تم العثور على $total_errors خطأ في $files_with_errors ملف. يرجى مراجعة التفاصيل أعلاه.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;'>🏠 العودة للوحة التحكم</a>";
echo "</div>";
?>
