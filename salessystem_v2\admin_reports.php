<?php
/**
 * صفحة التقارير الشاملة للمدير
 */
require_once __DIR__ . '/config/init.php';

// دوال التقارير
function exportReport($type, $data) {
    // تصدير التقرير حسب النوع
    switch ($type) {
        case 'excel':
            exportToExcel($data);
            break;
        case 'pdf':
            exportToPDF($data);
            break;
        case 'csv':
            exportToCSV($data);
            break;
    }
}

function generateCustomReport($params) {
    // إنشاء تقرير مخصص
    logActivity('custom_report_generated', 'reports', null, null, null, 'تم إنشاء تقرير مخصص');
    $_SESSION['success'] = 'تم إنشاء التقرير المخصص بنجاح';
}

function scheduleReport($params) {
    // جدولة التقرير
    global $db;

    $frequency = $params['frequency'] ?? 'daily';
    $email = $params['email'] ?? '';
    $time = $params['time'] ?? '09:00';
    $report_type = $params['report_type'] ?? 'overview';

    if (empty($email)) {
        $_SESSION['error'] = 'البريد الإلكتروني مطلوب';
        return;
    }

    // حفظ جدولة التقرير في قاعدة البيانات
    $stmt = $db->prepare("INSERT INTO scheduled_reports (report_type, frequency, email, time, created_at) VALUES (?, ?, ?, ?, NOW())");
    if ($stmt) {
        $stmt->bind_param("ssss", $report_type, $frequency, $email, $time);
        if ($stmt->execute()) {
            logActivity('report_scheduled', 'reports', null, null, null, "تم جدولة تقرير $report_type");
            $_SESSION['success'] = 'تم جدولة التقرير بنجاح';
        } else {
            $_SESSION['error'] = 'فشل في جدولة التقرير';
        }
        $stmt->close();
    }
}

function exportToExcel($data) {
    // تصدير إلى Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="report_' . date('Y-m-d') . '.xls"');
    echo $data;
    exit();
}

function exportToPDF($data) {
    // تصدير إلى PDF
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="report_' . date('Y-m-d') . '.pdf"');
    echo $data;
    exit();
}

function exportToCSV($data) {
    // تصدير إلى CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="report_' . date('Y-m-d') . '.csv"');
    echo $data;
    exit();
}
// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}
// التحقق من الصلاحيات
if (!hasAdminPermission('view_reports')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // معالجة الإجراءات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'export_report':
                $export_type = $_POST['export_type'] ?? 'excel';
                $report_data = $_POST['report_data'] ?? '';
                exportReport($export_type, $report_data);
                break;

            case 'generate_custom_report':
                $custom_params = $_POST['custom_params'] ?? [];
                generateCustomReport($custom_params);
                break;

            case 'schedule_report':
                $schedule_params = $_POST['schedule_params'] ?? [];
                scheduleReport($schedule_params);
                break;
        }
    }

    // معاملات التقرير
    $report_type = $_GET['type'] ?? 'overview';
    $date_from = $_GET['date_from'] ?? date('Y-m-01');
    $date_to = $_GET['date_to'] ?? date('Y-m-d');
    $user_filter = $_GET['user_id'] ?? '';

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Admin reports page error: ' . $e->getMessage(), __FILE__, __LINE__);
    $_SESSION['error'] = 'حدث خطأ في النظام: ' . $e->getMessage();
}
// جلب إحصائيات شاملة
$overview_stats = [];

// إحصائيات المستخدمين
$user_stats_query = "SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month,
    COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_week
    FROM users";

if (!empty($date_from) && !empty($date_to)) {
    $user_stats_query .= " WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59'";
}

$user_stats_result = $db->query($user_stats_query);
if (!$user_stats_result) {
    ErrorHandler::logDatabaseError($user_stats_query, $db->error);
    $user_stats = ['total_users' => 0, 'active_users' => 0, 'new_users_month' => 0, 'active_week' => 0];
} else {
    $user_stats = $user_stats_result->fetch_assoc();
}
// إحصائيات النشاط
$activity_query = "SELECT
    COUNT(*) as total_activities,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as hour_activities
    FROM activity_log";

if (!empty($date_from) && !empty($date_to)) {
    $activity_query .= " WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59'";
}

$activity_result = $db->query($activity_query);
if (!$activity_result) {
    ErrorHandler::logDatabaseError($activity_query, $db->error);
    $activity_stats = ['total_activities' => 0, 'today_activities' => 0, 'week_activities' => 0, 'hour_activities' => 0];
} else {
    $activity_stats = $activity_result->fetch_assoc();
}
// إحصائيات النظام الموحد
$db_stats = [];

// جلب إحصائيات مالية شاملة
$financial_query = "SELECT
    (SELECT COUNT(*) FROM sales WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as sales_count,
    (SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as sales_total,
    (SELECT COUNT(*) FROM purchases WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as purchases_count,
    (SELECT COALESCE(SUM(total_amount), 0) FROM purchases WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as purchases_total,
    (SELECT COUNT(*) FROM customers WHERE created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as customers_count";

$financial_result = $db->query($financial_query);
if (!$financial_result) {
    ErrorHandler::logDatabaseError($financial_query, $db->error);
    $financial_stats = [
        'sales_count' => 0, 'sales_total' => 0,
        'purchases_count' => 0, 'purchases_total' => 0,
        'customers_count' => 0
    ];
} else {
    $financial_stats = $financial_result->fetch_assoc();
}

$sales_count = $financial_stats['sales_count'];
$purchases_count = $financial_stats['purchases_count'];
$customers_count = $financial_stats['customers_count'];
// إحصائيات لكل مستخدم
$user_details_query = "SELECT
    u.id, u.username, u.full_name, u.status, u.created_at, u.last_login,
    (SELECT COUNT(*) FROM sales s WHERE s.user_id = u.id AND s.created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as sales_count,
    (SELECT COALESCE(SUM(total_amount), 0) FROM sales s WHERE s.user_id = u.id AND s.created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as sales_total,
    (SELECT COUNT(*) FROM purchases p WHERE p.user_id = u.id AND p.created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as purchases_count,
    (SELECT COALESCE(SUM(total_amount), 0) FROM purchases p WHERE p.user_id = u.id AND p.created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as purchases_total,
    (SELECT COUNT(*) FROM customers c WHERE c.user_id = u.id AND c.created_at BETWEEN '$date_from' AND '$date_to 23:59:59') as customers_count
    FROM users u";

if (!empty($user_filter)) {
    $user_details_query .= " WHERE u.id = " . intval($user_filter);
} else {
    $user_details_query .= " WHERE u.status = 'active'";
}

$user_details_query .= " ORDER BY u.created_at DESC";

$users_result = $db->query($user_details_query);
$db_stats = [];
if ($users_result) {
    while ($user = $users_result->fetch_assoc()) {
        $db_stats[] = $user;
    }
}
// حساب المجاميع
$total_sales = $sales_count;
$total_purchases = $purchases_count;
$total_customers = $customers_count;
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-chart-bar me-3"></i>
                        التقارير الشاملة
                    </h1>
                    <p class="text-muted mb-0">عرض وتحليل التقارير الشاملة للنظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-success" onclick="exportReport()">
                        <i class="fas fa-download"></i>
                        <span>تصدير التقرير</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-info" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        <span>طباعة</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-warning" onclick="scheduleReport()">
                        <i class="fas fa-clock"></i>
                        <span>جدولة التقرير</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3" id="reportFilters">
                        <div class="col-md-2">
                            <label for="type" class="modern-form-label">نوع التقرير</label>
                            <select class="modern-form-control" id="type" name="type">
                                <option value="overview" <?php echo $report_type === 'overview' ? 'selected' : ''; ?>>نظرة عامة</option>
                                <option value="users" <?php echo $report_type === 'users' ? 'selected' : ''; ?>>تقرير المستخدمين</option>
                                <option value="activity" <?php echo $report_type === 'activity' ? 'selected' : ''; ?>>تقرير النشاط</option>
                                <option value="system" <?php echo $report_type === 'system' ? 'selected' : ''; ?>>تقرير النظام</option>
                                <option value="financial" <?php echo $report_type === 'financial' ? 'selected' : ''; ?>>التقرير المالي</option>
                                <option value="performance" <?php echo $report_type === 'performance' ? 'selected' : ''; ?>>تقرير الأداء</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="user_id" class="modern-form-label">المستخدم</label>
                            <select class="modern-form-control" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php
                                $users_list = $db->query("SELECT id, username, full_name FROM users WHERE status = 'active' ORDER BY full_name");
                                while ($user = $users_list->fetch_assoc()) {
                                    $selected = ($user_filter == $user['id']) ? 'selected' : '';
                                    echo "<option value='{$user['id']}' $selected>{$user['full_name']} ({$user['username']})</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-primary me-2">
                                <i class="fas fa-search me-1"></i>تطبيق
                            </button>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="modern-btn modern-btn-outline" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
</form>
                </div>
            </div>
            <!-- إحصائيات سريعة -->
            <div class="row g-4 mb-4">
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي المستخدمين</div>
                                <div class="luxury-value"><?php echo number_format($user_stats['total_users']); ?></div>
                                <div class="luxury-change">
                                    <i class="fas fa-user-check me-1"></i>
                                    نشط: <?php echo $user_stats['active_users']; ?>
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي المبيعات</div>
                                <div class="luxury-value"><?php echo number_format($financial_stats['sales_total'], 2); ?> ر.س</div>
                                <div class="luxury-change">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    <?php echo $sales_count; ?> فاتورة
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي المشتريات</div>
                                <div class="luxury-value"><?php echo number_format($financial_stats['purchases_total'], 2); ?> ر.س</div>
                                <div class="luxury-change">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    <?php echo $purchases_count; ?> فاتورة
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">صافي الربح</div>
                                <div class="luxury-value <?php echo ($financial_stats['sales_total'] - $financial_stats['purchases_total']) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($financial_stats['sales_total'] - $financial_stats['purchases_total'], 2); ?> ر.س
                                </div>
                                <div class="luxury-change">
                                    <i class="fas fa-coins me-1"></i>
                                    <?php echo ($financial_stats['sales_total'] - $financial_stats['purchases_total']) >= 0 ? 'ربح' : 'خسارة'; ?>
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($report_type === 'overview'): ?>
            <!-- تقرير النظرة العامة -->
            <div class="row g-4 mb-5">
                <!-- إحصائيات المستخدمين -->
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي المستخدمين
                                </div>
                                <div class="stats-value">
                                    <?php echo number_format($user_stats['total_users']); ?>
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-user-check me-1"></i>
                                    نشط: <?php echo $user_stats['active_users']; ?>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات المبيعات -->
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي المبيعات
                                </div>
                                <div class="stats-value text-success">
                                    <?php echo number_format($total_sales); ?>
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    فاتورة مبيعات
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات المشتريات -->
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي المشتريات
                                </div>
                                <div class="stats-value text-danger">
                                    <?php echo number_format($total_purchases); ?>
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    فاتورة شراء
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات العملاء -->
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي العملاء
                                </div>
                                <div class="stats-value text-info">
                                    <?php echo number_format($total_customers); ?>
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-user-plus me-1"></i>
                                    عميل مسجل
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-user-friends"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- الرسوم البيانية -->
            <div class="row g-4 mb-4">
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-pie me-2"></i>
                                نشاط المستخدمين
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <canvas id="activityChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-bar me-2"></i>
                                توزيع البيانات
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <canvas id="dataChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($report_type === 'users'): ?>
            <!-- تقرير المستخدمين -->
            <div class="premium-card">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-users me-2"></i>
                        تقرير المستخدمين التفصيلي
                    </h5>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>الحالة</th>
                                    <th>المبيعات</th>
                                    <th>المشتريات</th>
                                    <th>العملاء</th>
                                    <th>صافي الربح</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($db_stats)): ?>
                                    <?php foreach ($db_stats as $user_stat): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-initial bg-primary rounded-circle">
                                                            <?php echo strtoupper(substr($user_stat['full_name'] ?? $user_stat['username'], 0, 1)); ?>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($user_stat['full_name'] ?? 'غير محدد'); ?></div>
                                                        <small class="text-muted">@<?php echo htmlspecialchars($user_stat['username']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $user_stat['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $user_stat['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-success fw-bold"><?php echo number_format($user_stat['sales_total'], 2); ?> ر.س</div>
                                                <small class="text-muted"><?php echo $user_stat['sales_count']; ?> فاتورة</small>
                                            </td>
                                            <td>
                                                <div class="text-danger fw-bold"><?php echo number_format($user_stat['purchases_total'], 2); ?> ر.س</div>
                                                <small class="text-muted"><?php echo $user_stat['purchases_count']; ?> فاتورة</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $user_stat['customers_count']; ?></span>
                                            </td>
                                            <td>
                                                <?php $profit = $user_stat['sales_total'] - $user_stat['purchases_total']; ?>
                                                <div class="fw-bold <?php echo $profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                    <?php echo number_format($profit, 2); ?> ر.س
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo $user_stat['last_login'] ? date('Y-m-d H:i', strtotime($user_stat['last_login'])) : 'لم يسجل دخول'; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="admin_user_details.php?id=<?php echo $user_stat['id']; ?>" class="btn btn-sm btn-outline-primary" title="التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="admin_financial.php?user_id=<?php echo $user_stat['id']; ?>" class="btn btn-sm btn-outline-success" title="التقرير المالي">
                                                        <i class="fas fa-chart-line"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>لا توجد بيانات</h5>
                                                <p>لا توجد بيانات مستخدمين للفترة المحددة</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main></div>
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}

/* تحسينات التقارير */
.avatar-sm {
    width: 32px;
    height: 32px;
}
.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    color: white;
}

/* تحسين الطباعة */
@media print {
    .modern-sidebar, .d-flex.gap-2, .modern-card-header .modern-btn {
        display: none !important;
    }
    .admin-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    .modern-table {
        font-size: 12px;
    }
    .modern-table th, .modern-table td {
        padding: 4px !important;
        border: 1px solid #000 !important;
    }
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للنشاط
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'doughnut',
    data: {
        labels: [
    'نشاط اليوم',
    'نشاط الأسبوع',
    'إجمالي النشاط'
],
        datasets: [{
            data: [
                <?php echo $activity_stats['today_activities']; ?>,
                <?php echo $activity_stats['week_activities']; ?>,
                <?php echo $activity_stats['total_activities']; ?>
            ],
            backgroundColor: [
    '#4e73df',
    '#1cc88a',
    '#36b9cc'
]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
// رسم بياني للبيانات
const dataCtx = document.getElementById('dataChart').getContext('2d');
const dataChart = new Chart(dataCtx, {
    type: 'bar',
    data: {
        labels: [t("sales"), t("purchases"), t("customers")],
        datasets: [{
            label: 'العدد',
            data: [
                <?php echo $total_sales; ?>,
                <?php echo $total_purchases; ?>,
                <?php echo $total_customers; ?>
            ],
            backgroundColor: [
    '#1cc88a',
    '#f6c23e',
    '#36b9cc'
]
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('type').value = 'overview';
    document.getElementById('user_id').value = '';
    document.getElementById('date_from').value = '<?php echo date('Y-m-01'); ?>';
    document.getElementById('date_to').value = '<?php echo date('Y-m-d'); ?>';
    document.getElementById('reportFilters').submit();
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تصدير التقرير
function exportReport() {
    Swal.fire({
        title: 'تصدير التقرير',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">نوع التصدير:</label>
                    <select id="exportType" class="form-select">
                        <option value="excel">Excel</option>
                        <option value="pdf">PDF</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">البيانات المطلوبة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeStats" checked>
                        <label class="form-check-label" for="includeStats">الإحصائيات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                        <label class="form-check-label" for="includeCharts">الرسوم البيانية</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeDetails" checked>
                        <label class="form-check-label" for="includeDetails">التفاصيل</label>
                    </div>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تصدير',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const exportType = document.getElementById('exportType').value;
            const includeStats = document.getElementById('includeStats').checked;
            const includeCharts = document.getElementById('includeCharts').checked;
            const includeDetails = document.getElementById('includeDetails').checked;

            return {
                type: exportType,
                stats: includeStats,
                charts: includeCharts,
                details: includeDetails
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', result.value.type);
            params.set('include_stats', result.value.stats ? '1' : '0');
            params.set('include_charts', result.value.charts ? '1' : '0');
            params.set('include_details', result.value.details ? '1' : '0');

            window.open('admin_export.php?' + params.toString(), '_blank');
        }
    });
}

// جدولة التقرير
function scheduleReport() {
    Swal.fire({
        title: 'جدولة التقرير',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">تكرار الإرسال:</label>
                    <select id="scheduleFrequency" class="form-select">
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني:</label>
                    <input type="email" id="scheduleEmail" class="form-control" placeholder="<EMAIL>">
                </div>
                <div class="mb-3">
                    <label class="form-label">وقت الإرسال:</label>
                    <input type="time" id="scheduleTime" class="form-control" value="09:00">
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'جدولة',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const frequency = document.getElementById('scheduleFrequency').value;
            const email = document.getElementById('scheduleEmail').value;
            const time = document.getElementById('scheduleTime').value;

            if (!email) {
                Swal.showValidationMessage('يرجى إدخال البريد الإلكتروني');
                return false;
            }

            return { frequency, email, time };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب جدولة التقرير
            fetch('admin_reports.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'schedule_report',
                    'schedule_params[frequency]': result.value.frequency,
                    'schedule_params[email]': result.value.email,
                    'schedule_params[time]': result.value.time,
                    'schedule_params[report_type]': '<?php echo $report_type; ?>'
                })
            }).then(response => {
                if (response.ok) {
                    Swal.fire('تم!', 'تم جدولة التقرير بنجاح', 'success');
                } else {
                    Swal.fire('خطأ!', 'فشل في جدولة التقرير', 'error');
                }
            });
        }
    });
}
</script>
<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>