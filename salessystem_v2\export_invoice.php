<?php
/**
 * ملف تصدير الفاتورة
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    http_response_code(403);
    exit('غير مصرح');
}

$invoice_id = intval($_GET['id'] ?? 0);
$type = $_GET['type'] ?? 'sale';
$format = $_GET['format'] ?? 'pdf';

if ($invoice_id <= 0) {
    http_response_code(400);
    exit('معرف الفاتورة غير صحيح');
}

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // جلب بيانات الفاتورة
    if ($type === 'sale') {
        $query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address, u.full_name as user_name 
                  FROM sales s 
                  LEFT JOIN customers c ON s.customer_id = c.id 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.id = ?";
    } else {
        $query = "SELECT p.*, u.full_name as user_name 
                  FROM purchases p 
                  LEFT JOIN users u ON p.user_id = u.id 
                  WHERE p.id = ?";
    }

    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();

    if (!$invoice) {
        throw new Exception("الفاتورة غير موجودة");
    }

    // تصدير حسب التنسيق
    switch ($format) {
        case 'pdf':
            exportToPDF($invoice, $type);
            break;
        case 'excel':
            exportToExcel($invoice, $type);
            break;
        case 'word':
            exportToWord($invoice, $type);
            break;
        default:
            throw new Exception("تنسيق التصدير غير مدعوم");
    }

} catch (Exception $e) {
    http_response_code(500);
    exit('خطأ في التصدير: ' . $e->getMessage());
}

function exportToPDF($invoice, $type) {
    // تصدير إلى PDF
    $filename = 'invoice_' . $invoice['invoice_number'] . '_' . date('Y-m-d') . '.pdf';
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    // محاكاة محتوى PDF
    $content = generateInvoiceContent($invoice, $type);
    echo $content;
    exit();
}

function exportToExcel($invoice, $type) {
    // تصدير إلى Excel
    $filename = 'invoice_' . $invoice['invoice_number'] . '_' . date('Y-m-d') . '.xls';
    
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    // إضافة BOM للدعم العربي
    echo chr(0xEF).chr(0xBB).chr(0xBF);
    
    // إنشاء جدول HTML للـ Excel
    echo '<table border="1">';
    echo '<tr><th colspan="4" style="text-align:center; font-size:18px; font-weight:bold;">فاتورة ' . ($type === 'sale' ? 'مبيعات' : 'مشتريات') . '</th></tr>';
    echo '<tr><td>رقم الفاتورة:</td><td>' . htmlspecialchars($invoice['invoice_number']) . '</td><td>التاريخ:</td><td>' . $invoice['date'] . '</td></tr>';
    echo '<tr><td>المستخدم:</td><td>' . htmlspecialchars($invoice['user_name'] ?? 'غير محدد') . '</td><td></td><td></td></tr>';
    
    if ($type === 'sale' && isset($invoice['customer_name'])) {
        echo '<tr><td>العميل:</td><td>' . htmlspecialchars($invoice['customer_name']) . '</td><td></td><td></td></tr>';
    }
    
    echo '<tr><td></td><td></td><td></td><td></td></tr>';
    echo '<tr><th>البيان</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>';
    echo '<tr><td>منتج تجريبي</td><td>1</td><td>' . number_format($invoice['subtotal'], 2) . '</td><td>' . number_format($invoice['subtotal'], 2) . '</td></tr>';
    echo '<tr><td></td><td></td><td></td><td></td></tr>';
    echo '<tr><td></td><td></td><td>المبلغ الفرعي:</td><td>' . number_format($invoice['subtotal'], 2) . '</td></tr>';
    echo '<tr><td></td><td></td><td>الضريبة:</td><td>' . number_format($invoice['tax_amount'], 2) . '</td></tr>';
    echo '<tr><td></td><td></td><td style="font-weight:bold;">الإجمالي:</td><td style="font-weight:bold;">' . number_format($invoice['total_amount'], 2) . '</td></tr>';
    echo '</table>';
    
    exit();
}

function exportToWord($invoice, $type) {
    // تصدير إلى Word
    $filename = 'invoice_' . $invoice['invoice_number'] . '_' . date('Y-m-d') . '.doc';
    
    header('Content-Type: application/msword; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    // إضافة BOM للدعم العربي
    echo chr(0xEF).chr(0xBB).chr(0xBF);
    
    // إنشاء محتوى Word
    echo '<html dir="rtl">';
    echo '<head><meta charset="utf-8"></head>';
    echo '<body style="font-family: Arial, sans-serif; direction: rtl;">';
    
    echo '<div style="text-align: center; margin-bottom: 30px;">';
    echo '<h1>فاتورة ' . ($type === 'sale' ? 'مبيعات' : 'مشتريات') . '</h1>';
    echo '<h2>' . htmlspecialchars($invoice['invoice_number']) . '</h2>';
    echo '</div>';
    
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr><td style="border: 1px solid #000; padding: 8px;"><strong>رقم الفاتورة:</strong></td><td style="border: 1px solid #000; padding: 8px;">' . htmlspecialchars($invoice['invoice_number']) . '</td></tr>';
    echo '<tr><td style="border: 1px solid #000; padding: 8px;"><strong>التاريخ:</strong></td><td style="border: 1px solid #000; padding: 8px;">' . $invoice['date'] . '</td></tr>';
    echo '<tr><td style="border: 1px solid #000; padding: 8px;"><strong>المستخدم:</strong></td><td style="border: 1px solid #000; padding: 8px;">' . htmlspecialchars($invoice['user_name'] ?? 'غير محدد') . '</td></tr>';
    
    if ($type === 'sale' && isset($invoice['customer_name'])) {
        echo '<tr><td style="border: 1px solid #000; padding: 8px;"><strong>العميل:</strong></td><td style="border: 1px solid #000; padding: 8px;">' . htmlspecialchars($invoice['customer_name']) . '</td></tr>';
    }
    
    echo '</table>';
    
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background-color: #f0f0f0;"><th style="border: 1px solid #000; padding: 8px;">البيان</th><th style="border: 1px solid #000; padding: 8px;">الكمية</th><th style="border: 1px solid #000; padding: 8px;">السعر</th><th style="border: 1px solid #000; padding: 8px;">الإجمالي</th></tr>';
    echo '<tr><td style="border: 1px solid #000; padding: 8px;">منتج تجريبي</td><td style="border: 1px solid #000; padding: 8px;">1</td><td style="border: 1px solid #000; padding: 8px;">' . number_format($invoice['subtotal'], 2) . '</td><td style="border: 1px solid #000; padding: 8px;">' . number_format($invoice['subtotal'], 2) . '</td></tr>';
    echo '</table>';
    
    echo '<div style="text-align: left; margin-top: 30px;">';
    echo '<p><strong>المبلغ الفرعي: </strong>' . number_format($invoice['subtotal'], 2) . ' ريال</p>';
    echo '<p><strong>الضريبة: </strong>' . number_format($invoice['tax_amount'], 2) . ' ريال</p>';
    echo '<p style="font-size: 18px;"><strong>الإجمالي: </strong>' . number_format($invoice['total_amount'], 2) . ' ريال</p>';
    echo '</div>';
    
    echo '<div style="text-align: center; margin-top: 50px;">';
    echo '<p>شكراً لتعاملكم معنا</p>';
    echo '</div>';
    
    echo '</body></html>';
    
    exit();
}

function generateInvoiceContent($invoice, $type) {
    // إنشاء محتوى الفاتورة (محاكاة PDF)
    $content = "فاتورة " . ($type === 'sale' ? 'مبيعات' : 'مشتريات') . "\n";
    $content .= "رقم الفاتورة: " . $invoice['invoice_number'] . "\n";
    $content .= "التاريخ: " . $invoice['date'] . "\n";
    $content .= "المستخدم: " . ($invoice['user_name'] ?? 'غير محدد') . "\n";
    
    if ($type === 'sale' && isset($invoice['customer_name'])) {
        $content .= "العميل: " . $invoice['customer_name'] . "\n";
    }
    
    $content .= "\n--- تفاصيل الفاتورة ---\n";
    $content .= "المبلغ الفرعي: " . number_format($invoice['subtotal'], 2) . " ريال\n";
    $content .= "الضريبة: " . number_format($invoice['tax_amount'], 2) . " ريال\n";
    $content .= "الإجمالي: " . number_format($invoice['total_amount'], 2) . " ريال\n";
    
    return $content;
}
?>
