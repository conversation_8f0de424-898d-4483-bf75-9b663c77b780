<?php
/**
 * ملف التهيئة الرئيسي للنظام الموحد
 * تم تحديثه للعمل مع قاعدة البيانات الموحدة
 */

// بدء الجلسة
session_start();

// تضمين نظام إدارة الأخطاء أولاً
require_once __DIR__ . '/../includes/error_handler.php';

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'sales01');
define('DB_PASSWORD', 'dNz35nd5@');
define('DB_NAME', 'u193708811_system_main');
define('DB_CHARSET', 'utf8mb4');

/**
 * دالة الاتصال بقاعدة البيانات الموحدة
 */
function getUnifiedDB() {
    static $connection = null;

    if ($connection === null) {
        try {
            $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);

            if ($connection->connect_error) {
                ErrorHandler::logError('CRITICAL', 'Database connection failed: ' . $connection->connect_error);
                return false;
            }

            // تعيين ترميز الأحرف
            $connection->set_charset(DB_CHARSET);

            // تعيين المنطقة الزمنية
            $connection->query("SET time_zone = '+03:00'");

        } catch (Exception $e) {
            ErrorHandler::logError('CRITICAL', 'Database connection exception: ' . $e->getMessage());
            return false;
        }
    }

    return $connection;
}

// تضمين إعدادات البريد الإلكتروني
require_once 'email_config.php';

// تهيئة قاعدة البيانات بشكل آمن
try {
    initializeDatabase();
} catch (Exception $e) {
    ErrorHandler::logError('CRITICAL', 'Failed to initialize database: ' . $e->getMessage());
    die('حدث خطأ في تهيئة قاعدة البيانات. يرجى الاتصال بالدعم الفني.');
}

// تضمين الملف المساعد لقاعدة البيانات
require_once __DIR__ . '/../includes/database_helper.php';

// تضمين نظام اللغات
require_once __DIR__ . '/../includes/language.php';

// التحقق من وجود قاعدة البيانات الموحدة وجميع الجداول
function ensureMainDatabase() {
    // استخدام دالة إنشاء الجداول الموحدة
    return createUnifiedTables();
}

// دالة للتوافق - تم دمجها في النظام الموحد
function createAdminTables($main_db = null) {
    // تم دمج هذه الوظيفة في createMissingTables()
    return createMissingTables();
}

// دالة للتأكد من وجود جداول المدير
function ensureAdminTables() {
    // في النظام الموحد، تم دمج هذه الوظيفة
    return createUnifiedTables();
}

// تأكد من وجود قاعدة البيانات الرئيسية وجداول المدير
ensureMainDatabase();
ensureAdminTables();

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من تسجيل دخول المدير
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// دالة للتحقق من صلاحيات المدير
function hasAdminPermission($permission) {
    if (!isAdminLoggedIn()) {
        return false;
    }

    // إذا كان المدير الرئيسي، له جميع الصلاحيات
    if (isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super']) {
        return true;
    }

    // التحقق من الصلاحيات المخزنة في الجلسة
    $permissions = $_SESSION['admin_permissions'] ?? [];

    // إذا كانت الصلاحيات مخزنة كـ array من strings (النظام الجديد)
    if (is_array($permissions) && in_array($permission, $permissions)) {
        return true;
    }

    // إذا كانت الصلاحيات مخزنة كـ associative array مع boolean values (النظام القديم)
    if (is_array($permissions) && isset($permissions[$permission]) && $permissions[$permission]) {
        return true;
    }

    // قائمة الصلاحيات الافتراضية للمديرين العاديين (للتوافق مع النظام القديم)
    $default_permissions = [
        'view_dashboard',
        'view_users',
        'view_activity',
        'view_reports',
        'view_all_data',
        'manage_system',
        'manage_admins'
    ];

    // إذا لم توجد صلاحيات محددة، استخدم الصلاحيات الافتراضية
    return in_array($permission, $default_permissions);
}

// دالة للتحقق من كون المدير هو المدير الرئيسي
function isSuperAdmin($admin_id = null) {
    // إذا لم يتم تمرير معرف، استخدم المدير الحالي
    if ($admin_id === null) {
        return isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super'];
    }

    // التحقق من قاعدة البيانات
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }

    try {
        // محاولة استخدام العمود المخصص أولاً
        $stmt = $db->prepare("SELECT is_super_admin FROM admins WHERE id = ? AND status = 'active'");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin_data = $result->fetch_assoc();
        $stmt->close();

        if ($admin_data && isset($admin_data['is_super_admin'])) {
            return (bool)$admin_data['is_super_admin'];
        }
    } catch (Exception $e) {
        // إذا فشل (العمود غير موجود)، استخدم المعايير القديمة
    }

    // استخدام المعايير القديمة كاحتياطي
    try {
        $stmt = $db->prepare("SELECT id, username FROM admins WHERE id = ? AND (username = 'admin' OR id = 1) AND status = 'active'");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $is_super = $result->num_rows > 0;
        $stmt->close();

        return $is_super;
    } catch (Exception $e) {
        return false;
    }
}

// دالة للتحقق من إمكانية التحكم في مدير معين
function canManageAdmin($target_admin_id) {
    // لا يمكن التحكم في النفس
    if ($target_admin_id == $_SESSION['admin_id']) {
        return false;
    }

    // إذا كان المدير المستهدف هو المدير الرئيسي، فقط المدير الرئيسي يمكنه التحكم فيه
    if (isSuperAdmin($target_admin_id)) {
        return isSuperAdmin(); // فقط المدير الرئيسي يمكنه التحكم في المدير الرئيسي
    }

    // المديرين العاديين يمكن للجميع التحكم فيهم (إذا كان لديهم صلاحية)
    return hasAdminPermission('manage_admins');
}

// دالة لتسجيل العمليات
function logActivity($action, $table_name = null, $record_id = null, $old_data = null, $new_data = null, $description = null) {
    try {
        $db = getUnifiedDB();

        // التحقق من وجود قاعدة البيانات والجدول
        if (!$db) {
            ErrorHandler::logError('WARNING', 'Database connection not available for activity logging');
            return false;
        }

        // التأكد من وجود جداول المدير
        ensureAdminTables();

        $user_id = null;
        $user_type = 'user';

        if (isAdminLoggedIn()) {
            $user_id = $_SESSION['admin_id'];
            $user_type = 'admin';
        } elseif (isLoggedIn()) {
            $user_id = $_SESSION['user_id'];
            $user_type = 'user';
        }

        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

        $old_data_json = $old_data ? json_encode($old_data) : null;
        $new_data_json = $new_data ? json_encode($new_data) : null;

        $stmt = $db->prepare("INSERT INTO activity_log (user_id, user_type, action, table_name, record_id, old_data, new_data, description, ip_address, user_agent)
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        if (!$stmt) {
            ErrorHandler::logDatabaseError("INSERT INTO activity_log", $db->error);
            return false;
        }

        $stmt->bind_param("isssssssss", $user_id, $user_type, $action, $table_name, $record_id, $old_data_json, $new_data_json, $description, $ip_address, $user_agent);
        $result = $stmt->execute();

        if (!$result) {
            ErrorHandler::logDatabaseError("INSERT INTO activity_log", $stmt->error);
        }

        $stmt->close();
        return $result;

    } catch (Exception $e) {
        ErrorHandler::logError('ERROR', 'Exception in logActivity: ' . $e->getMessage(), __FILE__, __LINE__);
        return false;
    }
}

// إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على اتصال قاعدة بيانات المستخدم الحالي (محسن للنظام الموحد)
function getCurrentUserDB() {
    // في النظام الموحد، نستخدم نفس الاتصال للجميع
    return getUnifiedDB();
}

// دالة لإنشاء الجداول المطلوبة (محسنة للنظام الموحد)
function createRequiredTables($db = null) {
    // في النظام الموحد، تم دمج جميع الجداول
    return createMissingTables();
}

// دالة لتحديث هيكل الجداول الموجودة
function updateTableStructure($db) {
    // قائمة الجداول والأعمدة المطلوب إضافتها
    $tables_to_update = [
        'customers' => [
            'customer_type' => "ALTER TABLE `customers` ADD COLUMN `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer' AFTER `address`",
            'updated_at' => "ALTER TABLE `customers` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
        ],
        'products' => [
            'description' => "ALTER TABLE `products` ADD COLUMN `description` text DEFAULT NULL AFTER `name`",
            'category' => "ALTER TABLE `products` ADD COLUMN `category` varchar(100) DEFAULT NULL AFTER `tax_rate`",
            'updated_at' => "ALTER TABLE `products` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
        ],
        'purchase_items' => [
            'product_name' => "ALTER TABLE `purchase_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
            'unit_price' => "ALTER TABLE `purchase_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
        ],
        'sale_items' => [
            'product_name' => "ALTER TABLE `sale_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
            'unit_price' => "ALTER TABLE `sale_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
        ]
    ];

    foreach ($tables_to_update as $table_name => $columns) {
        // التحقق من وجود الجدول أولاً
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        if ($check_table && $check_table->num_rows > 0) {
            foreach ($columns as $column_name => $alter_sql) {
                // التحقق من وجود العمود
                $check_column = $db->query("SHOW COLUMNS FROM `$table_name` LIKE '$column_name'");
                if ($check_column && $check_column->num_rows == 0) {
                    // العمود غير موجود، أضفه
                    $db->query($alter_sql);
                }
            }
        }
    }

    // إضافة الفهارس المفقودة
    addMissingIndexes($db);
}

// دالة لإضافة الفهارس المفقودة
function addMissingIndexes($db) {
    $indexes_to_add = [
        'customers' => [
            'idx_customer_type' => "ALTER TABLE `customers` ADD INDEX `idx_customer_type` (`customer_type`)"
        ],
        'products' => [
            'idx_category' => "ALTER TABLE `products` ADD INDEX `idx_category` (`category`)"
        ]
    ];

    foreach ($indexes_to_add as $table_name => $indexes) {
        // التحقق من وجود الجدول أولاً
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        if ($check_table && $check_table->num_rows > 0) {
            // جلب الفهارس الموجودة
            $existing_indexes = [];
            $indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
            if ($indexes_result) {
                while ($index = $indexes_result->fetch_assoc()) {
                    $existing_indexes[] = $index['Key_name'];
                }
            }

            // إضافة الفهارس المفقودة
            foreach ($indexes as $index_name => $alter_sql) {
                if (!in_array($index_name, $existing_indexes)) {
                    $db->query($alter_sql);
                }
            }
        }
    }
}

/**
 * دالة إنشاء الجداول المفقودة
 */
function createMissingTables() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // جدول إعدادات النظام
    $system_settings_sql = "CREATE TABLE IF NOT EXISTS `system_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
        `description` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    // جدول الموردين (منفصل عن العملاء)
    $suppliers_sql = "CREATE TABLE IF NOT EXISTS `suppliers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `email` varchar(100) DEFAULT NULL,
        `tax_number` varchar(50) DEFAULT NULL,
        `address` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_supplier_name` (`name`),
        KEY `idx_supplier_phone` (`phone`),
        KEY `idx_supplier_email` (`email`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    // جدول فئات المنتجات
    $product_categories_sql = "CREATE TABLE IF NOT EXISTS `product_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `category_name` (`name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    // جدول المدفوعات
    $payments_sql = "CREATE TABLE IF NOT EXISTS `payments` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_id` int(11) NOT NULL,
        `invoice_type` enum('sale','purchase') NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `payment_method` enum('cash','card','bank_transfer','check','installment','other') DEFAULT 'cash',
        `payment_date` date NOT NULL,
        `notes` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_invoice_id` (`invoice_id`),
        KEY `idx_invoice_type` (`invoice_type`),
        KEY `idx_payment_date` (`payment_date`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    // جدول الميزانيات
    $budgets_sql = "CREATE TABLE IF NOT EXISTS `budgets` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `budget_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `spent_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `remaining_amount` decimal(10,2) GENERATED ALWAYS AS (`budget_amount` - `spent_amount`) STORED,
        `start_date` date NOT NULL,
        `end_date` date NOT NULL,
        `status` enum('active','inactive','completed') DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_budget_dates` (`start_date`, `end_date`),
        KEY `idx_budget_status` (`status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    // قائمة الجداول المراد إنشاؤها
    $tables = [
        'system_settings' => $system_settings_sql,
        'product_categories' => $product_categories_sql,
        'suppliers' => $suppliers_sql,
        'payments' => $payments_sql,
        'budgets' => $budgets_sql
    ];

    $created_tables = [];
    $errors = [];

    foreach ($tables as $table_name => $sql) {
        try {
            if ($db->query($sql)) {
                $created_tables[] = $table_name;
            } else {
                $errors[] = "فشل في إنشاء جدول $table_name: " . $db->error;
            }
        } catch (Exception $e) {
            $errors[] = "خطأ في إنشاء جدول $table_name: " . $e->getMessage();
        }
    }

    // إدراج البيانات الأولية
    insertInitialSystemData();

    return [
        'success' => empty($errors),
        'created_tables' => $created_tables,
        'errors' => $errors
    ];
}

/**
 * دالة إدراج البيانات الأولية للنظام
 */
function insertInitialSystemData() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // إدراج إعدادات النظام الأساسية
    $default_settings = [
        ['company_name', 'نظام المبيعات والمشتريات', 'text', 'اسم الشركة'],
        ['company_address', '', 'text', 'عنوان الشركة'],
        ['company_phone', '', 'text', 'هاتف الشركة'],
        ['company_email', '', 'text', 'بريد الشركة الإلكتروني'],
        ['company_tax_number', '', 'text', 'الرقم الضريبي للشركة'],
        ['company_logo', '', 'text', 'شعار الشركة'],
        ['default_currency', 'ر.س', 'text', 'العملة الافتراضية'],
        ['default_tax_rate', '15', 'number', 'نسبة الضريبة الافتراضية'],
        ['invoice_prefix_sale', 'INV-', 'text', 'بادئة رقم فاتورة المبيعات'],
        ['invoice_prefix_purchase', 'PUR-', 'text', 'بادئة رقم فاتورة المشتريات'],
        ['auto_print_pos', '1', 'boolean', 'طباعة POS تلقائياً'],
        ['backup_enabled', '1', 'boolean', 'تفعيل النسخ الاحتياطي'],
        ['backup_frequency', 'daily', 'text', 'تكرار النسخ الاحتياطي'],
        ['system_language', 'ar', 'text', 'لغة النظام'],
        ['date_format', 'Y-m-d', 'text', 'تنسيق التاريخ'],
        ['time_format', 'H:i:s', 'text', 'تنسيق الوقت'],
        ['items_per_page', '20', 'number', 'عدد العناصر في الصفحة'],
        ['low_stock_alert', '10', 'number', 'تنبيه المخزون المنخفض'],
        ['email_notifications', '1', 'boolean', 'تفعيل إشعارات البريد الإلكتروني'],
        ['sms_notifications', '0', 'boolean', 'تفعيل إشعارات الرسائل النصية']
    ];

    // التحقق من وجود الإعدادات وإدراجها إذا لم تكن موجودة
    foreach ($default_settings as $setting) {
        $check_sql = "SELECT COUNT(*) as count FROM system_settings WHERE setting_key = ?";
        $check_stmt = $db->prepare($check_sql);
        if ($check_stmt) {
            $check_stmt->bind_param('s', $setting[0]);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] == 0) {
                $insert_sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_sql);
                if ($insert_stmt) {
                    $insert_stmt->bind_param('ssss', $setting[0], $setting[1], $setting[2], $setting[3]);
                    $insert_stmt->execute();
                }
            }
        }
    }

    // إدراج فئات المنتجات الأساسية
    $default_categories = [
        'إلكترونيات', 'ملابس', 'أغذية ومشروبات', 'مستلزمات منزلية',
        'كتب وقرطاسية', 'رياضة وترفيه', 'صحة وجمال', 'أدوات ومعدات',
        'خدمات', 'أخرى'
    ];

    foreach ($default_categories as $category) {
        $check_sql = "SELECT COUNT(*) as count FROM product_categories WHERE name = ?";
        $check_stmt = $db->prepare($check_sql);
        if ($check_stmt) {
            $check_stmt->bind_param('s', $category);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] == 0) {
                $insert_sql = "INSERT INTO product_categories (name) VALUES (?)";
                $insert_stmt = $db->prepare($insert_sql);
                if ($insert_stmt) {
                    $insert_stmt->bind_param('s', $category);
                    $insert_stmt->execute();
                }
            }
        }
    }

    return true;
}

/**
 * دالة إنشاء المدير الرئيسي (Super Admin)
 */
function createSuperAdmin() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // التحقق من وجود مدير رئيسي
    $check_sql = "SELECT COUNT(*) as count FROM admins WHERE role = 'super_admin'";
    $check_result = $db->query($check_sql);

    if ($check_result) {
        $row = $check_result->fetch_assoc();
        if ($row['count'] > 0) {
            // يوجد مدير رئيسي بالفعل
            return true;
        }
    }

    // بيانات المدير الرئيسي الافتراضية
    $super_admin_data = [
        'username' => 'superadmin',
        'email' => '<EMAIL>',
        'password' => password_hash('Admin@123456', PASSWORD_DEFAULT),
        'full_name' => 'المدير الرئيسي',
        'role' => 'super_admin',
        'status' => 'active',
        'permissions' => json_encode([
            'users' => ['view', 'create', 'edit', 'delete'],
            'admins' => ['view', 'create', 'edit', 'delete'],
            'customers' => ['view', 'create', 'edit', 'delete'],
            'suppliers' => ['view', 'create', 'edit', 'delete'],
            'products' => ['view', 'create', 'edit', 'delete'],
            'sales' => ['view', 'create', 'edit', 'delete'],
            'purchases' => ['view', 'create', 'edit', 'delete'],
            'payments' => ['view', 'create', 'edit', 'delete'],
            'budgets' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'export'],
            'settings' => ['view', 'edit'],
            'system' => ['backup', 'restore', 'maintenance'],
            'logs' => ['view', 'delete']
        ]),
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => null
    ];

    // إدراج المدير الرئيسي
    $columns = array_keys($super_admin_data);
    $placeholders = array_fill(0, count($super_admin_data), '?');
    $values = array_values($super_admin_data);

    $insert_sql = "INSERT INTO admins (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

    $stmt = $db->prepare($insert_sql);
    if ($stmt) {
        $types = str_repeat('s', count($values));
        $stmt->bind_param($types, ...$values);

        if ($stmt->execute()) {
            // تسجيل العملية في سجل الأنشطة
            $admin_id = $db->insert_id;
            logActivity('admin_created', 'admins', $admin_id, null, $super_admin_data, "تم إنشاء المدير الرئيسي: {$super_admin_data['username']}");

            return $admin_id;
        }
    }

    return false;
}

/**
 * دالة التحقق من وجود المدير الرئيسي وإنشاؤه إذا لم يكن موجوداً
 */
function ensureSuperAdminExists() {
    $db = getUnifiedDB();
    if (!$db) return false;

    // التحقق من وجود مدير رئيسي
    $check_sql = "SELECT id, username, email FROM admins WHERE role = 'super_admin' AND status = 'active' LIMIT 1";
    $check_result = $db->query($check_sql);

    if ($check_result && $check_result->num_rows > 0) {
        // يوجد مدير رئيسي نشط
        $admin = $check_result->fetch_assoc();
        return [
            'exists' => true,
            'admin_id' => $admin['id'],
            'username' => $admin['username'],
            'email' => $admin['email']
        ];
    }

    // لا يوجد مدير رئيسي، إنشاء واحد جديد
    $admin_id = createSuperAdmin();

    if ($admin_id) {
        return [
            'exists' => false,
            'created' => true,
            'admin_id' => $admin_id,
            'username' => 'superadmin',
            'email' => '<EMAIL>',
            'default_password' => 'Admin@123456'
        ];
    }

    return [
        'exists' => false,
        'created' => false,
        'error' => 'فشل في إنشاء المدير الرئيسي'
    ];
}

// تشغيل إنشاء الجداول المفقودة والمدير الرئيسي تلقائياً
try {
    createMissingTables();
    ensureSuperAdminExists();
} catch (Exception $e) {
    ErrorHandler::logError('WARNING', 'Failed to create missing tables or super admin: ' . $e->getMessage());
}

?>