<?php
/**
 * ملف التهيئة الرئيسي للنظام الموحد
 * تم تحديثه للعمل مع قاعدة البيانات الموحدة
 */

// بدء الجلسة
session_start();

// تضمين نظام إدارة الأخطاء أولاً
require_once __DIR__ . '/../includes/error_handler.php';

// تضمين ملف تكوين قاعدة البيانات الموحدة
require_once 'unified_db_config.php';



// تضمين إعدادات البريد الإلكتروني
require_once 'email_config.php';

// تهيئة قاعدة البيانات بشكل آمن
try {
    // التحقق من الاتصال بقاعدة البيانات
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }

    // إنشاء الجداول المفقودة والبيانات الأولية
    createAppTables();

} catch (Exception $e) {
    ErrorHandler::logError('CRITICAL', 'Failed to initialize database: ' . $e->getMessage());
    // لا نوقف النظام، فقط نسجل الخطأ
}

// تضمين الملف المساعد لقاعدة البيانات
require_once __DIR__ . '/../includes/database_helper.php';

// تضمين نظام اللغات
require_once __DIR__ . '/../includes/language.php';

// التحقق من وجود قاعدة البيانات الموحدة وجميع الجداول
function ensureMainDatabase() {
    // استخدام دالة إنشاء الجداول الموحدة
    return createUnifiedTables();
}

// دالة للتوافق - تم دمجها في النظام الموحد
function createAdminTables($main_db = null) {
    // تم دمج هذه الوظيفة في createAppTables()
    return createAppTables();
}

// دالة للتأكد من وجود جداول المدير
function ensureAdminTables() {
    // في النظام الموحد، تم دمج هذه الوظيفة
    return createUnifiedTables();
}

// تأكد من وجود قاعدة البيانات الرئيسية وجداول المدير
ensureMainDatabase();
ensureAdminTables();

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من تسجيل دخول المدير
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// دالة للتحقق من صلاحيات المدير
function hasAdminPermission($permission) {
    if (!isAdminLoggedIn()) {
        return false;
    }

    // إذا كان المدير الرئيسي، له جميع الصلاحيات
    if (isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super']) {
        return true;
    }

    // التحقق من الصلاحيات المخزنة في الجلسة
    $permissions = $_SESSION['admin_permissions'] ?? [];

    // إذا كانت الصلاحيات مخزنة كـ array من strings (النظام الجديد)
    if (is_array($permissions) && in_array($permission, $permissions)) {
        return true;
    }

    // إذا كانت الصلاحيات مخزنة كـ associative array مع boolean values (النظام القديم)
    if (is_array($permissions) && isset($permissions[$permission]) && $permissions[$permission]) {
        return true;
    }

    // قائمة الصلاحيات الافتراضية للمديرين العاديين (للتوافق مع النظام القديم)
    $default_permissions = [
        'view_dashboard',
        'view_users',
        'view_activity',
        'view_reports',
        'view_all_data',
        'manage_system',
        'manage_admins'
    ];

    // إذا لم توجد صلاحيات محددة، استخدم الصلاحيات الافتراضية
    return in_array($permission, $default_permissions);
}

// دالة للتحقق من كون المدير هو المدير الرئيسي
function isSuperAdmin($admin_id = null) {
    // إذا لم يتم تمرير معرف، استخدم المدير الحالي
    if ($admin_id === null) {
        return isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super'];
    }

    // التحقق من قاعدة البيانات
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }

    try {
        // محاولة استخدام العمود المخصص أولاً
        $stmt = $db->prepare("SELECT is_super_admin FROM admins WHERE id = ? AND status = 'active'");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin_data = $result->fetch_assoc();
        $stmt->close();

        if ($admin_data && isset($admin_data['is_super_admin'])) {
            return (bool)$admin_data['is_super_admin'];
        }
    } catch (Exception $e) {
        // إذا فشل (العمود غير موجود)، استخدم المعايير القديمة
    }

    // استخدام المعايير القديمة كاحتياطي
    try {
        $stmt = $db->prepare("SELECT id, username FROM admins WHERE id = ? AND (username = 'admin' OR id = 1) AND status = 'active'");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $is_super = $result->num_rows > 0;
        $stmt->close();

        return $is_super;
    } catch (Exception $e) {
        return false;
    }
}

// دالة للتحقق من إمكانية التحكم في مدير معين
function canManageAdmin($target_admin_id) {
    // لا يمكن التحكم في النفس
    if ($target_admin_id == $_SESSION['admin_id']) {
        return false;
    }

    // إذا كان المدير المستهدف هو المدير الرئيسي، فقط المدير الرئيسي يمكنه التحكم فيه
    if (isSuperAdmin($target_admin_id)) {
        return isSuperAdmin(); // فقط المدير الرئيسي يمكنه التحكم في المدير الرئيسي
    }

    // المديرين العاديين يمكن للجميع التحكم فيهم (إذا كان لديهم صلاحية)
    return hasAdminPermission('manage_admins');
}

// دالة لتسجيل العمليات
function logActivity($action, $table_name = null, $record_id = null, $old_data = null, $new_data = null, $description = null) {
    try {
        $db = getUnifiedDB();

        // التحقق من وجود قاعدة البيانات والجدول
        if (!$db) {
            ErrorHandler::logError('WARNING', 'Database connection not available for activity logging');
            return false;
        }

        // التأكد من وجود جداول المدير
        ensureAdminTables();

        $user_id = null;
        $user_type = 'user';

        if (isAdminLoggedIn()) {
            $user_id = $_SESSION['admin_id'];
            $user_type = 'admin';
        } elseif (isLoggedIn()) {
            $user_id = $_SESSION['user_id'];
            $user_type = 'user';
        }

        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

        $old_data_json = $old_data ? json_encode($old_data) : null;
        $new_data_json = $new_data ? json_encode($new_data) : null;

        $stmt = $db->prepare("INSERT INTO activity_log (user_id, user_type, action, table_name, record_id, old_data, new_data, description, ip_address, user_agent)
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        if (!$stmt) {
            ErrorHandler::logDatabaseError("INSERT INTO activity_log", $db->error);
            return false;
        }

        $stmt->bind_param("isssssssss", $user_id, $user_type, $action, $table_name, $record_id, $old_data_json, $new_data_json, $description, $ip_address, $user_agent);
        $result = $stmt->execute();

        if (!$result) {
            ErrorHandler::logDatabaseError("INSERT INTO activity_log", $stmt->error);
        }

        $stmt->close();
        return $result;

    } catch (Exception $e) {
        ErrorHandler::logError('ERROR', 'Exception in logActivity: ' . $e->getMessage(), __FILE__, __LINE__);
        return false;
    }
}

// إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على اتصال قاعدة بيانات المستخدم الحالي (محسن للنظام الموحد)
function getCurrentUserDB() {
    // في النظام الموحد، نستخدم نفس الاتصال للجميع
    return getUnifiedDB();
}

// دالة لإنشاء الجداول المطلوبة (محسنة للنظام الموحد)
function createRequiredTables($db = null) {
    // في النظام الموحد، تم دمج جميع الجداول
    return createAppTables();
}

// دالة لتحديث هيكل الجداول الموجودة
function updateTableStructure($db) {
    // قائمة الجداول والأعمدة المطلوب إضافتها
    $tables_to_update = [
        'customers' => [
            'customer_type' => "ALTER TABLE `customers` ADD COLUMN `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer' AFTER `address`",
            'updated_at' => "ALTER TABLE `customers` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
        ],
        'products' => [
            'description' => "ALTER TABLE `products` ADD COLUMN `description` text DEFAULT NULL AFTER `name`",
            'category' => "ALTER TABLE `products` ADD COLUMN `category` varchar(100) DEFAULT NULL AFTER `tax_rate`",
            'updated_at' => "ALTER TABLE `products` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
        ],
        'purchase_items' => [
            'product_name' => "ALTER TABLE `purchase_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
            'unit_price' => "ALTER TABLE `purchase_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
        ],
        'sale_items' => [
            'product_name' => "ALTER TABLE `sale_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
            'unit_price' => "ALTER TABLE `sale_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
        ]
    ];

    foreach ($tables_to_update as $table_name => $columns) {
        // التحقق من وجود الجدول أولاً
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        if ($check_table && $check_table->num_rows > 0) {
            foreach ($columns as $column_name => $alter_sql) {
                // التحقق من وجود العمود
                $check_column = $db->query("SHOW COLUMNS FROM `$table_name` LIKE '$column_name'");
                if ($check_column && $check_column->num_rows == 0) {
                    // العمود غير موجود، أضفه
                    $db->query($alter_sql);
                }
            }
        }
    }

    // إضافة الفهارس المفقودة
    addMissingIndexes($db);
}

// دالة لإضافة الفهارس المفقودة
function addMissingIndexes($db) {
    $indexes_to_add = [
        'customers' => [
            'idx_customer_type' => "ALTER TABLE `customers` ADD INDEX `idx_customer_type` (`customer_type`)"
        ],
        'products' => [
            'idx_category' => "ALTER TABLE `products` ADD INDEX `idx_category` (`category`)"
        ]
    ];

    foreach ($indexes_to_add as $table_name => $indexes) {
        // التحقق من وجود الجدول أولاً
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        if ($check_table && $check_table->num_rows > 0) {
            // جلب الفهارس الموجودة
            $existing_indexes = [];
            $indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
            if ($indexes_result) {
                while ($index = $indexes_result->fetch_assoc()) {
                    $existing_indexes[] = $index['Key_name'];
                }
            }

            // إضافة الفهارس المفقودة
            foreach ($indexes as $index_name => $alter_sql) {
                if (!in_array($index_name, $existing_indexes)) {
                    $db->query($alter_sql);
                }
            }
        }
    }
}







?>