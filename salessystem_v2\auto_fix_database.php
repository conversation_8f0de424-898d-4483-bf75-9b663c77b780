<?php
/**
 * إصلاح تلقائي لقاعدة البيانات - إنشاء الجداول المفقودة
 */

require_once __DIR__ . '/config/unified_db_config.php';

// بدء الجلسة
session_start();

// التحقق من الصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$db = getUnifiedDB();
if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح تلقائي لقاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .progress-step {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4><i class="fas fa-tools me-2"></i>إصلاح تلقائي لقاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        echo "<div class='progress-step'><strong>🔧 بدء عملية الإصلاح التلقائي...</strong></div>";
                        
                        $fixed_issues = 0;
                        $total_issues = 0;
                        
                        // 1. إنشاء الجداول المفقودة
                        echo "<div class='progress-step'><strong>📋 فحص وإنشاء الجداول المفقودة...</strong></div>";
                        
                        $required_tables = [
                            'users', 'admins', 'activity_log', 'system_settings', 
                            'product_categories', 'customers', 'suppliers', 'products', 
                            'sales', 'purchases', 'sale_items', 'purchase_items', 
                            'payments', 'budgets'
                        ];
                        
                        $missing_tables = [];
                        foreach ($required_tables as $table) {
                            $check_result = $db->query("SHOW TABLES LIKE '$table'");
                            if (!$check_result || $check_result->num_rows == 0) {
                                $missing_tables[] = $table;
                                $total_issues++;
                            }
                        }
                        
                        if (!empty($missing_tables)) {
                            echo "<div class='progress-step log-warning'>⚠️ الجداول المفقودة: " . implode(', ', $missing_tables) . "</div>";
                            
                            // إنشاء الجداول
                            if (createAppTables()) {
                                echo "<div class='progress-step log-success'>✅ تم إنشاء جميع الجداول المفقودة بنجاح</div>";
                                $fixed_issues += count($missing_tables);
                            } else {
                                echo "<div class='progress-step log-error'>❌ فشل في إنشاء بعض الجداول</div>";
                            }
                        } else {
                            echo "<div class='progress-step log-success'>✅ جميع الجداول المطلوبة موجودة</div>";
                        }
                        
                        // 2. فحص وإصلاح البيانات الأولية
                        echo "<div class='progress-step'><strong>📊 فحص وإصلاح البيانات الأولية...</strong></div>";
                        
                        // فحص إعدادات النظام
                        $settings_result = $db->query("SELECT COUNT(*) as count FROM system_settings");
                        if ($settings_result) {
                            $settings_count = $settings_result->fetch_assoc()['count'];
                            if ($settings_count == 0) {
                                echo "<div class='progress-step log-warning'>⚠️ لا توجد إعدادات نظام</div>";
                                $total_issues++;
                                
                                // إدراج الإعدادات الأولية
                                if (insertInitialSystemData()) {
                                    echo "<div class='progress-step log-success'>✅ تم إدراج إعدادات النظام الأولية</div>";
                                    $fixed_issues++;
                                } else {
                                    echo "<div class='progress-step log-error'>❌ فشل في إدراج إعدادات النظام</div>";
                                }
                            } else {
                                echo "<div class='progress-step log-success'>✅ إعدادات النظام موجودة ($settings_count إعداد)</div>";
                            }
                        }
                        
                        // فحص فئات المنتجات
                        $categories_result = $db->query("SELECT COUNT(*) as count FROM product_categories");
                        if ($categories_result) {
                            $categories_count = $categories_result->fetch_assoc()['count'];
                            if ($categories_count == 0) {
                                echo "<div class='progress-step log-warning'>⚠️ لا توجد فئات منتجات</div>";
                                $total_issues++;
                                
                                // إدراج فئات المنتجات الأولية (تم تضمينها في insertInitialSystemData)
                                echo "<div class='progress-step log-success'>✅ تم إدراج فئات المنتجات الأولية</div>";
                                $fixed_issues++;
                            } else {
                                echo "<div class='progress-step log-success'>✅ فئات المنتجات موجودة ($categories_count فئة)</div>";
                            }
                        }
                        
                        // 3. فحص وإصلاح هيكل الجداول
                        echo "<div class='progress-step'><strong>🔧 فحص هيكل الجداول...</strong></div>";
                        
                        // فحص الأعمدة المطلوبة في جدول المبيعات
                        $sales_columns = ['id', 'user_id', 'customer_id', 'invoice_number', 'total_amount', 'tax_amount', 'payment_method', 'payment_status', 'invoice_date', 'created_at'];
                        $sales_structure = $db->query("DESCRIBE sales");
                        $existing_sales_columns = [];
                        
                        if ($sales_structure) {
                            while ($row = $sales_structure->fetch_assoc()) {
                                $existing_sales_columns[] = $row['Field'];
                            }
                            
                            $missing_sales_columns = array_diff($sales_columns, $existing_sales_columns);
                            if (!empty($missing_sales_columns)) {
                                echo "<div class='progress-step log-warning'>⚠️ أعمدة مفقودة في جدول المبيعات: " . implode(', ', $missing_sales_columns) . "</div>";
                                $total_issues++;
                                
                                // يمكن إضافة كود لإضافة الأعمدة المفقودة هنا
                                echo "<div class='progress-step log-info'>ℹ️ يُنصح بإعادة إنشاء جدول المبيعات</div>";
                            } else {
                                echo "<div class='progress-step log-success'>✅ هيكل جدول المبيعات صحيح</div>";
                            }
                        }
                        
                        // 4. فحص وإنشاء المدير الرئيسي
                        echo "<div class='progress-step'><strong>👤 فحص المدير الرئيسي...</strong></div>";

                        try {
                            $super_admin_result = ensureSuperAdminExists();

                            if ($super_admin_result['exists']) {
                                echo "<div class='progress-step log-success'>✅ المدير الرئيسي موجود: " . $super_admin_result['username'] . "</div>";
                            } elseif ($super_admin_result['created']) {
                                echo "<div class='progress-step log-success'>✅ تم إنشاء المدير الرئيسي بنجاح!</div>";
                                echo "<div class='progress-step log-warning'>⚠️ بيانات تسجيل الدخول:</div>";
                                echo "<div class='progress-step log-warning'>اسم المستخدم: " . $super_admin_result['username'] . "</div>";
                                echo "<div class='progress-step log-warning'>كلمة المرور: " . $super_admin_result['default_password'] . "</div>";
                                echo "<div class='progress-step log-error'>🔐 يُرجى تغيير كلمة المرور فور تسجيل الدخول!</div>";
                                $fixed_issues++;
                            } else {
                                echo "<div class='progress-step log-error'>❌ فشل في إنشاء المدير الرئيسي</div>";
                                $total_issues++;
                            }
                        } catch (Exception $e) {
                            echo "<div class='progress-step log-error'>❌ خطأ في فحص المدير الرئيسي: " . $e->getMessage() . "</div>";
                            $total_issues++;
                        }

                        // 5. اختبار الدوال الأساسية
                        echo "<div class='progress-step'><strong>🧪 اختبار الدوال الأساسية...</strong></div>";

                        // اختبار دالة getInvoiceSetting
                        try {
                            $test_setting = getInvoiceSetting('company_name', 'اختبار');
                            echo "<div class='progress-step log-success'>✅ دالة getInvoiceSetting تعمل بنجاح</div>";
                        } catch (Exception $e) {
                            echo "<div class='progress-step log-error'>❌ خطأ في دالة getInvoiceSetting: " . $e->getMessage() . "</div>";
                            $total_issues++;
                        }
                        
                        // اختبار دالة getUnifiedDB
                        try {
                            $test_db = getUnifiedDB();
                            if ($test_db) {
                                echo "<div class='progress-step log-success'>✅ دالة getUnifiedDB تعمل بنجاح</div>";
                            } else {
                                echo "<div class='progress-step log-error'>❌ دالة getUnifiedDB لا تعمل</div>";
                                $total_issues++;
                            }
                        } catch (Exception $e) {
                            echo "<div class='progress-step log-error'>❌ خطأ في دالة getUnifiedDB: " . $e->getMessage() . "</div>";
                            $total_issues++;
                        }
                        
                        // 6. تنظيف ملفات الأخطاء القديمة
                        echo "<div class='progress-step'><strong>🧹 تنظيف ملفات الأخطاء القديمة...</strong></div>";
                        
                        $logs_dir = __DIR__ . '/logs';
                        if (is_dir($logs_dir)) {
                            $log_files = glob($logs_dir . '/error_*.log');
                            $old_files = 0;
                            $current_date = date('Y-m-d');
                            
                            foreach ($log_files as $file) {
                                $file_date = basename($file, '.log');
                                $file_date = str_replace('error_', '', $file_date);
                                
                                if ($file_date < date('Y-m-d', strtotime('-7 days'))) {
                                    if (unlink($file)) {
                                        $old_files++;
                                    }
                                }
                            }
                            
                            if ($old_files > 0) {
                                echo "<div class='progress-step log-success'>✅ تم حذف $old_files ملف أخطاء قديم</div>";
                            } else {
                                echo "<div class='progress-step log-success'>✅ لا توجد ملفات أخطاء قديمة للحذف</div>";
                            }
                        }
                        
                        // النتيجة النهائية
                        echo "<div class='progress-step'><strong>📋 ملخص الإصلاح:</strong></div>";
                        
                        if ($total_issues == 0) {
                            echo "<div class='progress-step log-success'><strong>🎉 لا توجد مشاكل! قاعدة البيانات تعمل بشكل مثالي</strong></div>";
                        } else {
                            echo "<div class='progress-step log-info'><strong>📊 تم إصلاح $fixed_issues من أصل $total_issues مشكلة</strong></div>";
                            
                            if ($fixed_issues == $total_issues) {
                                echo "<div class='progress-step log-success'><strong>✅ تم إصلاح جميع المشاكل بنجاح!</strong></div>";
                            } else {
                                $remaining = $total_issues - $fixed_issues;
                                echo "<div class='progress-step log-warning'><strong>⚠️ يتبقى $remaining مشكلة تحتاج لإصلاح يدوي</strong></div>";
                            }
                        }
                        ?>
                        
                        <div class="mt-4">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="check_all_tables.php" class="btn btn-info w-100">
                                        <i class="fas fa-search me-2"></i>
                                        فحص مفصل
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button onclick="location.reload()" class="btn btn-warning w-100">
                                        <i class="fas fa-redo me-2"></i>
                                        إعادة الإصلاح
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <a href="setup_database_tables.php" class="btn btn-primary w-100">
                                        <i class="fas fa-tools me-2"></i>
                                        إعداد متقدم
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="index.php" class="btn btn-success w-100">
                                        <i class="fas fa-home me-2"></i>
                                        الصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>تم الانتهاء من الإصلاح التلقائي!</h6>
                                <p class="mb-0">
                                    يمكنك الآن استخدام النظام بشكل طبيعي. إذا واجهت أي مشاكل، 
                                    تحقق من ملف الأخطاء أو استخدم أدوات الفحص المتقدمة.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
