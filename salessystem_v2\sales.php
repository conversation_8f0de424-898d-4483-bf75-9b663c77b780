<?php
// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/invoice_functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// جلب إعدادات المبيعات
$currency_symbol = getInvoiceSetting('currency_symbol', 'ريال');
$decimal_places = intval(getInvoiceSetting('decimal_places', '2'));

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo __('manage_sales'); ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_sale.php" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo __('add_sale'); ?>
        </a>
    </div>
</div>

<?php
// إحصائيات المبيعات مع البادئة وفلترة user_id
$username = $_SESSION['username'];
$customers_table = getUserTableName('customers', $username);
$sales_table = getUserTableName('sales', $username);
$stats_query = "SELECT
                COUNT(id) as total_count,
                SUM(total_amount) as total_amount,
                SUM(tax_amount) as total_tax,
                SUM(paid_amount) as total_paid,
                SUM(remaining_amount) as total_remaining,
                COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
                COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_count,
                COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_count,
                MIN(date) as first_date,
                MAX(date) as last_date
                FROM `$sales_table` WHERE user_id = {$_SESSION['user_id']}";
$stats = $db->query($stats_query)->fetch_assoc();
?>

<!-- الفلاتر والإحصائيات أعلى الجدول -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    <?php echo __('filters'); ?>
                </h5>
            </div>
            <div class="card-body">
                <!-- نموذج الفلاتر -->
                <form method="GET" action="sales.php" class="mb-4">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="<?php echo __('search_placeholder'); ?>" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label"><?php echo __('customer'); ?></label>
                                <select class="form-select" id="customer_id" name="customer_id">
                                    <option value=""><?php echo __('all_customers'); ?></option>
                                    <?php
                                    $customers = $db->query("SELECT id, name FROM `$customers_table` WHERE user_id = {$_SESSION['user_id']} ORDER BY name");
                                    while ($customer = $customers->fetch_assoc()):
                                        $selected = (isset($_GET['customer_id']) && $_GET['customer_id'] == $customer['id']) ? 'selected' : '';
                                    ?>
                                        <option value="<?php echo $customer['id']; ?>" <?php echo $selected; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : ''; ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="date_to" class="form-label"><?php echo __('date_to'); ?></label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : ''; ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="payment_status" class="form-label">حالة الدفع</label>
                                <select class="form-select" id="payment_status" name="payment_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="paid" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'paid') ? 'selected' : ''; ?>>مدفوع بالكامل</option>
                                    <option value="partial" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'partial') ? 'selected' : ''; ?>>مدفوع جزئياً</option>
                                    <option value="unpaid" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'unpaid') ? 'selected' : ''; ?>><?php echo t("unpaid"); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="cash" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'cash') ? 'selected' : ''; ?>>نقدي</option>
                                    <option value="card" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'card') ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                                    <option value="bank_transfer" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                                    <option value="check" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'check') ? 'selected' : ''; ?>>شيك</option>
                                    <option value="installment" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'installment') ? 'selected' : ''; ?>>تقسيط</option>
                                    <option value="other" <?php echo (isset($_GET['payment_method']) && $_GET['payment_method'] == 'other') ? 'selected' : ''; ?>>أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-search me-1"></i>
                                <?php echo __('search'); ?>
                            </button>
                            <a href="sales.php" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                <?php echo __('reset'); ?>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نموذج التعديل المستقل -->
<div id="editFormOverlay" class="edit-form-overlay" style="display: none;" onclick="closeOnBackdrop(event)">
    <div class="edit-form-container" onclick="event.stopPropagation()">
        <div class="edit-form-header">
            <h4 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل فاتورة المبيعات
            </h4>
            <button type="button" class="btn btn-outline-light btn-sm" onclick="hideEditForm()">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
        <div class="edit-form-body">
            <div id="editFormContent">
                <div class="text-center py-5">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل نموذج التعديل...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المبيعات -->
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('sales_list'); ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo __('invoice_number'); ?></th>
                                <th><?php echo __('date'); ?></th>
                                <th><?php echo __('customer_name'); ?></th>
                                <th><?php echo __('total'); ?></th>
                                <th>طريقة الدفع</th>
                                <th>حالة الدفع</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                // بناء الاستعلام مع الفلاتر والبادئة وفلترة user_id
                                $where_conditions = [];
                                $params = [];
                                $param_types = '';

                                // فلتر user_id (أمان)
                                $where_conditions[] = "s.user_id = ?";
                                $params[] = $_SESSION['user_id'];
                                $param_types .= 'i';

                                // فلتر البحث
                                if (isset($_GET['search']) && !empty($_GET['search'])) {
                                    $search = '%' . $_GET['search'] . '%';
                                    $where_conditions[] = "(s.invoice_number LIKE ? OR c.name LIKE ?)";
                                    $params[] = $search;
                                    $params[] = $search;
                                    $param_types .= 'ss';
                                }

                                // فلتر العميل
                                if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
                                    $where_conditions[] = "s.customer_id = ?";
                                    $params[] = $_GET['customer_id'];
                                    $param_types .= 'i';
                                }

                                // فلتر التاريخ من
                                if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
                                    $where_conditions[] = "s.date >= ?";
                                    $params[] = $_GET['date_from'];
                                    $param_types .= 's';
                                }

                                // فلتر التاريخ إلى
                                if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
                                    $where_conditions[] = "s.date <= ?";
                                    $params[] = $_GET['date_to'];
                                    $param_types .= 's';
                                }

                                // فلتر حالة الدفع
                                if (isset($_GET['payment_status']) && !empty($_GET['payment_status'])) {
                                    $where_conditions[] = "s.payment_status = ?";
                                    $params[] = $_GET['payment_status'];
                                    $param_types .= 's';
                                }

                                // فلتر طريقة الدفع
                                if (isset($_GET['payment_method']) && !empty($_GET['payment_method'])) {
                                    $where_conditions[] = "s.payment_method = ?";
                                    $params[] = $_GET['payment_method'];
                                    $param_types .= 's';
                                }

                                // بناء جملة WHERE
                                $where_clause = '';
                                if (!empty($where_conditions)) {
                                    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
                                }

                                $query = "SELECT s.id, s.invoice_number, s.date, s.total_amount, s.tax_amount,
                                                 s.payment_method, s.payment_status, s.paid_amount, s.remaining_amount,
                                                 c.name AS customer_name
                                          FROM `$sales_table` s
                                          LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                                          $where_clause
                                          ORDER BY s.id DESC";

                                if (!empty($params)) {
                                    $stmt = $db->prepare($query);
                                    $stmt->bind_param($param_types, ...$params);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                } else {
                                    $result = $db->query($query);
                                }

                                if (!$result) {
                                    throw new Exception($db->error);
                                }

                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                        // ترجمة طريقة الدفع
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'check' => 'شيك',
                                            'installment' => 'تقسيط',
                                            'other' => 'أخرى'
                                        ];

                                        // ترجمة حالة الدفع
                                        $payment_statuses = [
                                            'paid' => 'مدفوع بالكامل',
                                            'partial' => 'مدفوع جزئياً',
                                            'unpaid' => t("unpaid")
                                        ];

                                        // ألوان حالة الدفع
                                        $status_colors = [
                                            'paid' => 'success',
                                            'partial' => 'warning',
                                            'unpaid' => 'danger'
                                        ];
                                        ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo $row['invoice_number']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($row['date'])); ?></td>
                                <td><?php echo htmlspecialchars($row['customer_name'] ?? __('no_customer')); ?></td>
                                <td><?php echo formatCurrency($row['total_amount']); ?></td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo $payment_methods[$row['payment_method']] ?? $row['payment_method']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $status_colors[$row['payment_status']] ?? 'secondary'; ?>">
                                        <?php echo $payment_statuses[$row['payment_status']] ?? $row['payment_status']; ?>
                                    </span>
                                </td>
                                <td><?php echo formatCurrency($row['paid_amount'] ?? 0); ?></td>
                                <td>
                                    <?php if (($row['remaining_amount'] ?? 0) > 0): ?>
                                        <span class="text-danger fw-bold"><?php echo formatCurrency($row['remaining_amount']); ?></span>
                                    <?php else: ?>
                                        <span class="text-success"><?php echo formatCurrency(0); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewInvoice(<?php echo $row['id']; ?>, 'sale')" title="<?php echo __('view'); ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="edit_sale.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="<?php echo __('edit'); ?>">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete_sale.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete_sale'); ?>');" title="<?php echo __('delete'); ?>">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="print_invoice.php?id=<?php echo $row['id']; ?>&type=sale" class="btn btn-sm btn-secondary" target="_blank" title="<?php echo __('print'); ?>">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="10" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("Error displaying sales: " . $e->getMessage());
                                echo '<tr><td colspan="10" class="text-center">' . __('error') . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات أسفل الجدول -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المبيعات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body py-2">
                                <h5 class="mb-1"><?php echo $stats['total_count']; ?></h5>
                                <small><?php echo __('total_sales'); ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <h6 class="mb-1"><?php echo number_format($stats['total_amount'], 0); ?></h6>
                                <small><?php echo __('total_amount'); ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body py-2">
                                <h6 class="mb-1"><?php echo number_format($stats['total_paid'] ?? 0, 0); ?></h6>
                                <small>إجمالي المدفوع</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body py-2">
                                <h6 class="mb-1"><?php echo number_format($stats['total_remaining'] ?? 0, 0); ?></h6>
                                <small>إجمالي المتبقي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <h5 class="mb-1"><?php echo $stats['paid_count'] ?? 0; ?></h5>
                                <small>مدفوع بالكامل</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body py-2">
                                <h5 class="mb-1"><?php echo $stats['unpaid_count'] ?? 0; ?></h5>
                                <small><?php echo t("unpaid"); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- النوافذ المنبثقة -->
<!-- نافذة عرض الفاتورة -->
<div class="modal fade" id="viewInvoiceModal" tabindex="-1" aria-labelledby="viewInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewInvoiceModalLabel">
                    <i class="fas fa-eye me-2"></i>
                    عرض الفاتورة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="viewInvoiceContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="printInvoiceBtn">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>



<script>
// متغيرات عامة
let currentInvoiceId = null;
let currentInvoiceType = null;

// دالة عرض الفاتورة في نافذة منبثقة
function viewInvoice(invoiceId, type) {
    currentInvoiceId = invoiceId;
    currentInvoiceType = type;

    // إظهار النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
    modal.show();

    // تحميل محتوى الفاتورة
    const contentDiv = document.getElementById('viewInvoiceContent');
    contentDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    // طلب AJAX لتحميل محتوى الفاتورة
    fetch(`view_${type}.php?id=${invoiceId}&modal=1`)
        .then(response => response.text())
        .then(data => {
            contentDiv.innerHTML = data;
        })
        .catch(error => {
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل الفاتورة
                </div>
            `;
        });
}

// دالة إظهار نموذج التعديل
function showEditForm(invoiceId) {
    const editOverlay = document.getElementById('editFormOverlay');
    const editContent = document.getElementById('editFormContent');

    // إظهار الطبقة العلوية مع تأثير الظهور
    editOverlay.style.display = 'flex';
    editOverlay.style.opacity = '0';

    // تأثير الظهور التدريجي
    setTimeout(() => {
        editOverlay.style.transition = 'opacity 0.3s ease-in-out';
        editOverlay.style.opacity = '1';
    }, 10);

    // منع التمرير في الخلفية
    document.body.style.overflow = 'hidden';

    // تحميل نموذج التعديل
    editContent.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل نموذج التعديل...</p>
        </div>
    `;

    // طلب AJAX لتحميل نموذج التعديل
    fetch(`edit_sale.php?id=${invoiceId}&inline=1`)
        .then(response => response.text())
        .then(data => {
            editContent.innerHTML = data;

            // تأخير قصير لضمان تحميل DOM بشكل كامل
            setTimeout(() => {
                // تشغيل دالة التهيئة إذا كانت موجودة
                if (typeof initializeForm === 'function') {
                    initializeForm();
                }
            }, 100);
        })
        .catch(error => {
            editContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل نموذج التعديل
                </div>
            `;
        });
}

// دالة إخفاء نموذج التعديل
function hideEditForm() {
    const editOverlay = document.getElementById('editFormOverlay');

    // تأثير الاختفاء التدريجي
    editOverlay.style.transition = 'opacity 0.3s ease-in-out';
    editOverlay.style.opacity = '0';

    // إعادة تفعيل التمرير في الخلفية
    document.body.style.overflow = 'auto';

    // إخفاء الطبقة العلوية بعد انتهاء التأثير
    setTimeout(() => {
        editOverlay.style.display = 'none';
        editOverlay.style.transition = '';
    }, 300);
}

// دالة إغلاق النموذج بالضغط على الخلفية
function closeOnBackdrop(event) {
    if (event.target === event.currentTarget) {
        hideEditForm();
    }
}

// إغلاق النموذج بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const editOverlay = document.getElementById('editFormOverlay');
        if (editOverlay && editOverlay.style.display !== 'none') {
            hideEditForm();
        }
    }
});

// دالة طباعة الفاتورة
document.getElementById('printInvoiceBtn').addEventListener('click', function() {
    if (currentInvoiceId && currentInvoiceType) {
        window.open(`print_invoice.php?id=${currentInvoiceId}&type=${currentInvoiceType}`, '_blank');
    }
});

// دالة إظهار رسالة نجاح
function showSuccessMessage(message) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه إلى الصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 3 ثوانٍ
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

</script>

<?php require_once 'includes/footer.php'; ?>