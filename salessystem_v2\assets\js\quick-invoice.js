/**
 * وظائف الفواتير السريعة
 * يتم تضمينها في جميع صفحات النظام
 */

// دالة لإضافة صف عنصر جديد
function addQuickItemRow(product = null) {
    const tbody = document.getElementById('quickItemsBody');
    if (!tbody) return;
    
    const rowId = Date.now() + itemCounter;
    itemCounter++;

    const row = document.createElement('tr');
    row.id = `quick_row_${rowId}`;

    // عمود المنتج
    const productCell = document.createElement('td');
    const productSelect = document.createElement('select');
    productSelect.className = 'form-select form-select-sm quick-product-select';
    productSelect.name = 'product_id[]';
    productSelect.required = true;
    productSelect.style.fontSize = '10px';

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر منتج --';
    productSelect.appendChild(defaultOption);

    // إضافة خيار "إضافة منتج جديد"
    const addNewOption = document.createElement('option');
    addNewOption.value = 'new';
    addNewOption.textContent = '-- إضافة منتج جديد --';
    addNewOption.className = 'text-success fw-bold';
    productSelect.appendChild(addNewOption);

    if (typeof quickProducts !== 'undefined') {
        quickProducts.forEach(p => {
            const option = document.createElement('option');
            option.value = p.id;
            // عرض اسم المنتج مع التصنيف إذا كان متوفراً
            const displayName = p.category ? `${p.name} (${p.category})` : p.name;
            option.textContent = displayName;
            option.dataset.price = p.price;
            option.dataset.taxRate = p.tax_rate;
            option.dataset.category = p.category || '';

            if (product && product.id == p.id) {
                option.selected = true;
            }

            productSelect.appendChild(option);
        });
    }

    productSelect.addEventListener('change', function() {
        const selectedValue = this.value;
        const row = this.closest('tr');

        if (selectedValue === 'new') {
            // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
            this.value = '';
            alert('ميزة إضافة منتج جديد ستكون متاحة قريباً');
        } else if (selectedValue !== '') {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price || 0;
            const taxRate = selectedOption.dataset.taxRate || 15;

            row.querySelector('.quick-price-input').value = price;
            row.querySelector('.quick-tax-rate-input').value = taxRate;

            calculateQuickRowTotal(row);
            updateQuickInvoiceSummary();
        }
    });

    productCell.appendChild(productSelect);
    row.appendChild(productCell);

    // عمود الكمية
    const quantityCell = document.createElement('td');
    const quantityInput = document.createElement('input');
    quantityInput.type = 'number';
    quantityInput.className = 'form-control form-control-sm quick-quantity-input';
    quantityInput.name = 'quantity[]';
    quantityInput.min = '1';
    quantityInput.step = '0.01';
    quantityInput.value = product ? product.quantity : '1';
    quantityInput.required = true;
    quantityInput.style.fontSize = '10px';
    quantityInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    quantityCell.appendChild(quantityInput);
    row.appendChild(quantityCell);

    // عمود السعر
    const priceCell = document.createElement('td');
    const priceInput = document.createElement('input');
    priceInput.type = 'number';
    priceInput.className = 'form-control form-control-sm quick-price-input';
    priceInput.name = 'price[]';
    priceInput.step = '0.01';
    priceInput.min = '0';
    priceInput.value = product ? product.unit_price : '0';
    priceInput.required = true;
    priceInput.style.fontSize = '10px';
    priceInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    priceCell.appendChild(priceInput);
    row.appendChild(priceCell);

    // عمود الضريبة
    const taxCell = document.createElement('td');
    const taxInput = document.createElement('input');
    taxInput.type = 'number';
    taxInput.className = 'form-control form-control-sm quick-tax-rate-input';
    taxInput.name = 'tax_rate[]';
    taxInput.step = '0.01';
    taxInput.min = '0';
    taxInput.value = product ? product.tax_rate : '15';
    taxInput.required = true;
    taxInput.style.fontSize = '10px';
    taxInput.addEventListener('input', function() {
        calculateQuickRowTotal(this.closest('tr'));
        updateQuickInvoiceSummary();
    });
    taxCell.appendChild(taxInput);
    row.appendChild(taxCell);

    // عمود المجموع
    const totalCell = document.createElement('td');
    totalCell.className = 'quick-row-total text-end';
    totalCell.style.fontSize = '10px';
    totalCell.style.fontWeight = '600';
    totalCell.textContent = '0.00 ر.س';
    row.appendChild(totalCell);

    // عمود الإجراءات
    const actionsCell = document.createElement('td');
    actionsCell.className = 'text-center';
    const deleteBtn = document.createElement('button');
    deleteBtn.type = 'button';
    deleteBtn.className = 'btn btn-sm btn-outline-danger';
    deleteBtn.style.fontSize = '10px';
    deleteBtn.style.padding = '2px 6px';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.title = 'حذف الصف';
    deleteBtn.addEventListener('click', function() {
        if (confirm('هل تريد حذف هذا الصف؟')) {
            row.remove();
            updateQuickInvoiceSummary();
        }
    });
    actionsCell.appendChild(deleteBtn);
    row.appendChild(actionsCell);

    tbody.appendChild(row);

    // إذا تم تمرير منتج، احسب المجموع للصف
    if (product) {
        calculateQuickRowTotal(row);
    }

    return row;
}

// دالة لحساب مجموع الصف
function calculateQuickRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quick-quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.quick-price-input').value) || 0;
    const taxRate = parseFloat(row.querySelector('.quick-tax-rate-input').value) || 0;

    const subtotal = quantity * price;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    row.querySelector('.quick-row-total').textContent = total.toFixed(2) + ' ر.س';
}

// دالة لتحديث ملخص الفاتورة
function updateQuickInvoiceSummary() {
    let subtotal = 0;
    let taxAmount = 0;

    const rows = document.querySelectorAll('#quickItemsBody tr');
    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.quick-quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.quick-price-input').value) || 0;
        const taxRate = parseFloat(row.querySelector('.quick-tax-rate-input').value) || 0;

        const rowSubtotal = quantity * price;
        const rowTax = rowSubtotal * (taxRate / 100);

        subtotal += rowSubtotal;
        taxAmount += rowTax;
    });

    const total = subtotal + taxAmount;

    const subtotalCell = document.getElementById('quickSubtotalCell');
    const taxCell = document.getElementById('quickTaxCell');
    const totalCell = document.getElementById('quickTotalCell');

    if (subtotalCell) subtotalCell.textContent = subtotal.toFixed(2);
    if (taxCell) taxCell.textContent = taxAmount.toFixed(2);
    if (totalCell) totalCell.textContent = total.toFixed(2);

    // تحديث حقول الدفع تلقائياً
    updateQuickPaymentFields();
}

// دالة لتحديث حقول الدفع بناءً على حالة الدفع في الفاتورة السريعة
function updateQuickPaymentFields() {
    const paymentStatusElement = document.getElementById('quickPaymentStatus');
    const paidAmountField = document.getElementById('quickPaidAmount');
    const totalCell = document.getElementById('quickTotalCell');

    // التحقق من وجود العناصر
    if (!paymentStatusElement || !paidAmountField || !totalCell) {
        return;
    }

    const totalAmount = parseFloat(totalCell.textContent) || 0;
    const paymentStatus = paymentStatusElement.value;

    if (paymentStatus === 'paid') {
        // إذا كان مدفوع بالكامل، اجعل المبلغ المدفوع = الإجمالي
        paidAmountField.value = totalAmount.toFixed(2);
    } else if (paymentStatus === 'unpaid') {
        // إذا كان غير مدفوع، اجعل المبلغ المدفوع = 0
        paidAmountField.value = '0.00';
    } else if (paymentStatus === 'partial') {
        // إذا كان مدفوع جزئياً، اتركه كما هو أو اجعله نصف المبلغ
        if (parseFloat(paidAmountField.value) === 0) {
            paidAmountField.value = (totalAmount / 2).toFixed(2);
        }
    }

    calculateQuickRemainingAmount();
}

// دالة لحساب المبلغ المتبقي في الفاتورة السريعة
function calculateQuickRemainingAmount() {
    const totalCell = document.getElementById('quickTotalCell');
    const paidAmountField = document.getElementById('quickPaidAmount');

    // التحقق من وجود العناصر
    if (!totalCell || !paidAmountField) {
        return;
    }

    const totalAmount = parseFloat(totalCell.textContent) || 0;
    const paidAmount = parseFloat(paidAmountField.value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    // عرض المبلغ المتبقي في ملخص الفاتورة (اختياري)
    let remainingCell = document.getElementById('quickRemainingCell');
    if (remainingCell) {
        remainingCell.textContent = remainingAmount.toFixed(2) + ' ر.س';

        // تغيير لون النص حسب الحالة
        if (remainingAmount > 0) {
            remainingCell.className = 'text-danger fw-bold';
        } else if (remainingAmount < 0) {
            remainingCell.className = 'text-warning fw-bold';
        } else {
            remainingCell.className = 'text-success fw-bold';
        }
    }
}

// دالة لإضافة عميل/مورد جديد إلى قاعدة البيانات
async function saveQuickNewCustomer() {
    const customerName = document.getElementById('quick_new_customer_name').value.trim();
    const customerPhone = document.getElementById('quick_new_customer_phone').value.trim();
    const customerEmail = document.getElementById('quick_new_customer_email').value.trim();
    const customerTaxNumber = document.getElementById('quick_new_customer_tax_number').value.trim();
    const customerAddress = document.getElementById('quick_new_customer_address').value.trim();
    const customerType = document.getElementById('quick_customer_type').value || 'customer';

    const entityName = customerType === 'supplier' ? 'المورد' : 'العميل';

    if (!customerName) {
        alert(`يرجى إدخال اسم ${entityName}`);
        return;
    }

    if (customerName.length < 2) {
        alert(`اسم ${entityName} يجب أن يكون أكثر من حرف واحد`);
        return;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (customerEmail && !isValidEmail(customerEmail)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }

    try {
        // إرسال بيانات العميل الجديد إلى الخادم
        const formData = new FormData();
        formData.append('name', customerName);
        formData.append('phone', customerPhone);
        formData.append('email', customerEmail);
        formData.append('tax_number', customerTaxNumber);
        formData.append('address', customerAddress);
        formData.append('customer_type', customerType);
        formData.append('action', 'add_customer');

        const response = await fetch('ajax_handler.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const result = await response.json();

        if (result.success) {
            // إضافة العميل الجديد إلى القائمة المنسدلة
            const customerSelect = document.getElementById('quickCustomer');
            const option = document.createElement('option');
            option.value = result.customer_id;
            option.textContent = customerName;
            option.selected = true;

            // إضافة الخيار قبل الخيار الأخير (إضافة عميل جديد)
            customerSelect.insertBefore(option, customerSelect.options[1]);

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('quickAddCustomerModal'));
            modal.hide();

            // إعادة تعيين نموذج إضافة العميل
            document.getElementById('quickAddCustomerForm').reset();

            // عرض رسالة نجاح
            alert(`تم إضافة ${entityName} بنجاح`);
        } else {
            console.error('Server error:', result);
            alert(`حدث خطأ أثناء إضافة ${entityName}: ` + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error saving customer:', error);
        alert(`حدث خطأ في الشبكة أثناء إضافة ${entityName}. يرجى المحاولة مرة أخرى.`);
    }
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// تهيئة وظائف الفواتير السريعة
function initQuickInvoice() {
    // معالجة زر إضافة عنصر
    const addItemBtn = document.getElementById('addQuickItemBtn');
    if (addItemBtn) {
        addItemBtn.addEventListener('click', function() {
            addQuickItemRow();
        });
    }

    // معالجة زر حفظ العميل الجديد
    const saveCustomerBtn = document.getElementById('saveQuickNewCustomer');
    if (saveCustomerBtn) {
        saveCustomerBtn.addEventListener('click', saveQuickNewCustomer);
    }

    // معالجة تغيير قائمة العملاء
    const customerSelect = document.getElementById('quickCustomer');
    if (customerSelect) {
        customerSelect.addEventListener('change', function() {
            if (this.value === 'new') {
                // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
                this.value = '';

                // فتح النافذة المنبثقة لإضافة عميل جديد
                const modal = new bootstrap.Modal(document.getElementById('quickAddCustomerModal'));
                modal.show();
            }
        });
    }

    // إرسال النموذج
    const quickForm = document.getElementById('quickInvoiceForm');
    if (quickForm) {
        quickForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // التحقق من وجود أصناف
            const items = document.querySelectorAll('#quickItemsBody tr');
            if (items.length === 0) {
                alert('يجب إضافة عنصر واحد على الأقل');
                return;
            }

            // التحقق من ملء جميع الحقول المطلوبة
            let isValid = true;
            let hasValidItem = false;

            items.forEach(item => {
                const productSelect = item.querySelector('.quick-product-select');
                const quantity = item.querySelector('.quick-quantity-input');
                const price = item.querySelector('.quick-price-input');

                if (productSelect && quantity && price) {
                    const productValue = productSelect.value;
                    const quantityValue = quantity.value;
                    const priceValue = price.value;

                    if (productValue && quantityValue && priceValue) {
                        hasValidItem = true;
                    } else if (productValue || quantityValue || priceValue) {
                        // إذا كان هناك قيم جزئية، فهذا خطأ
                        isValid = false;
                    }
                }
            });

            if (!hasValidItem) {
                alert('يجب إضافة عنصر واحد على الأقل مع ملء جميع الحقول');
                return;
            }

            if (!isValid) {
                alert('يرجى ملء جميع حقول العناصر أو حذف العناصر الفارغة');
                return;
            }

            // التحقق من العميل/المورد
            const customer = document.getElementById('quickCustomer').value;
            const invoiceType = document.getElementById('invoiceType').value;
            const entityName = invoiceType === 'sale' ? 'العميل' : 'المورد';

            if (!customer) {
                alert(`يرجى اختيار ${entityName}`);
                return;
            }

            // إرسال النموذج
            this.submit();
        });
    }
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initQuickInvoice);
