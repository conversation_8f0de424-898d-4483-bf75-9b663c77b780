<?php
// دالة لإنشاء رقم فاتورة تلقائي
function generateInvoiceNumber($prefix = 'INV') {
    // استخدام ترقيم بسيط بدلاً من uniqid لتجنب الأرقام الطويلة
    return $prefix . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// دالة لحساب الضريبة والمجموع
function calculateTaxAndTotal($items) {
    $subtotal = 0;
    $total_tax = 0;

    foreach ($items as $item) {
        $item_total = $item['quantity'] * $item['unit_price'];
        $item_tax = $item_total * ($item['tax_rate'] / 100);

        $subtotal += $item_total;
        $total_tax += $item_tax;
    }

    $total = $subtotal + $total_tax;

    return [
        'subtotal' => $subtotal,
        'tax_amount' => $total_tax,
        'total' => $total
    ];
}
function resetDBConnection($db) {
    while ($db->more_results()) {
        $db->next_result();
        if ($result = $db->store_result()) {
            $result->free();
        }
    }
    return $db;
}

// دالة لعرض رسائل الخطأ أو النجاح مع تحسينات
function displayMessages() {
    if (isset($_SESSION['error'])) {
        echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
        echo htmlspecialchars($_SESSION['error']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['error']);
    }

    if (isset($_SESSION['success'])) {
        echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
        echo '<i class="fas fa-check-circle me-2"></i>';
        echo htmlspecialchars($_SESSION['success']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['success']);
    }

    if (isset($_SESSION['warning'])) {
        echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">';
        echo '<i class="fas fa-exclamation-circle me-2"></i>';
        echo htmlspecialchars($_SESSION['warning']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['warning']);
    }

    if (isset($_SESSION['info'])) {
        echo '<div class="alert alert-info alert-dismissible fade show" role="alert">';
        echo '<i class="fas fa-info-circle me-2"></i>';
        echo htmlspecialchars($_SESSION['info']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['info']);
    }
}

// دالة لعرض رسالة خطأ مع تسجيلها
function showErrorMessage($message, $logDetails = []) {
    if (class_exists('ErrorHandler')) {
        ErrorHandler::logError('USER_ERROR', $message, '', 0, $logDetails);
    }
    $_SESSION['error'] = $message;
}

// دالة لعرض رسالة تحذير مع تسجيلها
function showWarningMessage($message, $logDetails = []) {
    if (class_exists('ErrorHandler')) {
        ErrorHandler::logError('WARNING', $message, '', 0, $logDetails);
    }
    $_SESSION['warning'] = $message;
}

// دالة لعرض رسالة نجاح
function showSuccessMessage($message) {
    $_SESSION['success'] = $message;
}

// دالة لعرض رسالة معلومات
function showInfoMessage($message) {
    $_SESSION['info'] = $message;
}

// دالة للتعامل مع أخطاء قاعدة البيانات مع تسجيل محسن
function handleDBError($db, $query = '', $location = null) {
    $error_message = __('database_error') . ": " . $db->error;

    // تسجيل الخطأ باستخدام نظام الأخطاء الجديد
    if (class_exists('ErrorHandler')) {
        ErrorHandler::logDatabaseError($query, $db->error);
    } else {
        error_log($error_message . " in query: " . $query);
    }

    $_SESSION['error'] = "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.";

    if ($location) {
        header("Location: " . $location);
        exit();
    }

    return false;
}

// دالة للتحقق من وجود الجداول المطلوبة
function checkRequiredTables($db) {
    $required_tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    $missing_tables = [];

    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            $missing_tables[] = $table;
        }
    }

    return $missing_tables;
}

// دالة للتحقق من سلامة قاعدة البيانات
function validateDatabase($db) {
    if (!$db || $db->connect_error) {
        return [
            'status' => false,
            'message' => 'فشل الاتصال بقاعدة البيانات'
        ];
    }

    $missing_tables = checkRequiredTables($db);

    if (!empty($missing_tables)) {
        return [
            'status' => false,
            'message' => 'جداول مفقودة: ' . implode(', ', $missing_tables)
        ];
    }

    return [
        'status' => true,
        'message' => 'قاعدة البيانات سليمة'
    ];
}

// دالة للتحقق من نتائج الاستعلام
function checkQueryResult($result, $db, $query = '', $location = null) {
    if (!$result) {
        return handleDBError($db, $query, $location);
    }
    return true;
}

/**
 * إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
 * @param string $email البريد الإلكتروني
 * @param string $reset_link رابط إعادة التعيين
 * @param string $user_name اسم المستخدم
 * @return bool نجح الإرسال أم لا
 */
function sendPasswordResetEmail($email, $reset_link, $user_name) {
    // في بيئة الإنتاج، يجب استخدام مكتبة إرسال بريد إلكتروني مثل PHPMailer
    // هنا سنقوم بمحاكاة الإرسال وتسجيل المعلومات

    $subject = "إعادة تعيين كلمة المرور - نظام إدارة المبيعات";
    $message = "
    <html>
    <head>
        <title>إعادة تعيين كلمة المرور</title>
        <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>إعادة تعيين كلمة المرور</h2>
            </div>
            <div class='content'>
                <p>مرحباً " . htmlspecialchars($user_name) . ",</p>
                <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام إدارة المبيعات.</p>
                <p>انقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='" . $reset_link . "' class='button'>إعادة تعيين كلمة المرور</a>
                </p>
                <p><strong>ملاحظة:</strong> هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
                <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            </div>
            <div class='footer'>
                <p>نظام إدارة المبيعات &copy; " . date('Y') . "</p>
            </div>
        </div>
    </body>
    </html>
    ";

    // في بيئة الإنتاج، استخدم هذا الكود:
    /*
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: <EMAIL>' . "\r\n";

    return mail($email, $subject, $message, $headers);
    */

    // للاختبار، سنسجل المعلومات في ملف log
    $log_message = "Password reset email would be sent to: $email\n";
    $log_message .= "Reset link: $reset_link\n";
    $log_message .= "User: $user_name\n";
    $log_message .= "Time: " . date('Y-m-d H:i:s') . "\n";
    $log_message .= "---\n";

    error_log($log_message, 3, 'password_reset_log.txt');

    // إرجاع true للمحاكاة
    return true;
}

/**
 * تنظيف الرموز المنتهية الصلاحية
 */
function cleanExpiredTokens() {
    $db = getDB();
    if ($db) {
        // التحقق من وجود الأعمدة أولاً
        $check_columns = $db->query("SHOW COLUMNS FROM users LIKE 'reset_token_expires'");
        if ($check_columns && $check_columns->num_rows > 0) {
            $stmt = $db->prepare("UPDATE users SET reset_token = NULL, reset_token_expires = NULL WHERE reset_token_expires < NOW()");
            if ($stmt) {
                $stmt->execute();
                $stmt->close();
            }
        }
    }
}

/**
 * إنشاء رمز إعادة تعيين آمن
 * @return string
 */
function generateResetToken() {
    return bin2hex(random_bytes(32));
}

/**
 * التحقق من صحة رمز إعادة التعيين
 * @param string $token
 * @return array|false
 */
function validateResetToken($token) {
    if (empty($token)) {
        return false;
    }

    $db = getDB();
    if (!$db) {
        return false;
    }

    // التحقق من وجود الأعمدة أولاً
    $check_columns = $db->query("SHOW COLUMNS FROM users LIKE 'reset_token'");
    if (!$check_columns || $check_columns->num_rows == 0) {
        return false;
    }

    $stmt = $db->prepare("SELECT id, username, email, full_name FROM users WHERE reset_token = ? AND reset_token_expires > NOW() AND status = 'active'");
    if (!$stmt) {
        return false;
    }

    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user_data = $result->fetch_assoc();
        $stmt->close();
        return $user_data;
    }

    $stmt->close();
    return false;
}





?>