<?php
/**
 * اختبار الطباعة التلقائية للفواتير السريعة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getUnifiedDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<h2>🧪 اختبار الطباعة التلقائية للفواتير السريعة</h2>";

// جلب عينة من الفواتير للاختبار
$sales_query = "SELECT id, invoice_number, total_amount, date FROM sales WHERE user_id = ? ORDER BY id DESC LIMIT 3";
$sales_stmt = $db->prepare($sales_query);
$sales_stmt->bind_param("i", $_SESSION['user_id']);
$sales_stmt->execute();
$sales_result = $sales_stmt->get_result();
$sales = $sales_result->fetch_all(MYSQLI_ASSOC);
$sales_stmt->close();

$purchases_query = "SELECT id, invoice_number, total_amount, date FROM purchases WHERE user_id = ? ORDER BY id DESC LIMIT 3";
$purchases_stmt = $db->prepare($purchases_query);
$purchases_stmt->bind_param("i", $_SESSION['user_id']);
$purchases_stmt->execute();
$purchases_result = $purchases_stmt->get_result();
$purchases = $purchases_result->fetch_all(MYSQLI_ASSOC);
$purchases_stmt->close();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-file-invoice-dollar me-2"></i>اختبار فواتير المبيعات</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($sales)): ?>
                        <p class="text-muted">انقر على "محاكاة الحفظ" لاختبار الطباعة التلقائية:</p>
                        
                        <?php foreach ($sales as $sale): ?>
                        <div class="border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>فاتورة رقم:</strong> <?php echo htmlspecialchars($sale['invoice_number']); ?><br>
                                    <strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($sale['date'])); ?><br>
                                    <strong>المبلغ:</strong> <?php echo number_format($sale['total_amount'], 2); ?> ر.س
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-success btn-sm w-100 mb-2" 
                                            onclick="simulateQuickSave(<?php echo $sale['id']; ?>, 'sale')">
                                        <i class="fas fa-bolt me-1"></i>
                                        محاكاة الحفظ
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm w-100" 
                                            onclick="testDirectPrint(<?php echo $sale['id']; ?>, 'sale')">
                                        <i class="fas fa-receipt me-1"></i>
                                        طباعة مباشرة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد فواتير مبيعات للاختبار. 
                            <a href="index.php" class="alert-link">أضف فاتورة سريعة</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-shopping-cart me-2"></i>اختبار فواتير المشتريات</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($purchases)): ?>
                        <p class="text-muted">انقر على "محاكاة الحفظ" لاختبار الطباعة التلقائية:</p>
                        
                        <?php foreach ($purchases as $purchase): ?>
                        <div class="border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>فاتورة رقم:</strong> <?php echo htmlspecialchars($purchase['invoice_number']); ?><br>
                                    <strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($purchase['date'])); ?><br>
                                    <strong>المبلغ:</strong> <?php echo number_format($purchase['total_amount'], 2); ?> ر.س
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-danger btn-sm w-100 mb-2" 
                                            onclick="simulateQuickSave(<?php echo $purchase['id']; ?>, 'purchase')">
                                        <i class="fas fa-bolt me-1"></i>
                                        محاكاة الحفظ
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm w-100" 
                                            onclick="testDirectPrint(<?php echo $purchase['id']; ?>, 'purchase')">
                                        <i class="fas fa-receipt me-1"></i>
                                        طباعة مباشرة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد فواتير مشتريات للاختبار. 
                            <a href="index.php" class="alert-link">أضف فاتورة سريعة</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-cogs me-2"></i>إعدادات الاختبار</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="testPrintWidth" class="form-label">عرض الطباعة:</label>
                            <select id="testPrintWidth" class="form-select">
                                <option value="58">58mm (صغير)</option>
                                <option value="80" selected>80mm (متوسط)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="testAutoPrint" class="form-label">الطباعة التلقائية:</label>
                            <select id="testAutoPrint" class="form-select">
                                <option value="1" selected>مفعلة</option>
                                <option value="0">معطلة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="testDelay" class="form-label">تأخير الطباعة (ثانية):</label>
                            <select id="testDelay" class="form-select">
                                <option value="500">0.5</option>
                                <option value="1000" selected>1</option>
                                <option value="2000">2</option>
                                <option value="3000">3</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-info-circle me-2"></i>نتائج الاختبار</h5>
                </div>
                <div class="card-body">
                    <div id="testResults">
                        <p class="text-muted">لم يتم تشغيل أي اختبارات بعد...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="index.php" class="btn btn-primary">
            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
        </a>
        <a href="test_pos_print.php" class="btn btn-secondary">
            <i class="fas fa-print me-2"></i>اختبار طباعة POS
        </a>
        <button type="button" class="btn btn-warning" onclick="clearTestResults()">
            <i class="fas fa-trash me-2"></i>مسح النتائج
        </button>
    </div>
</div>

<script src="assets/js/pos-print.js"></script>
<script>
let testCounter = 0;

// محاكاة حفظ فاتورة سريعة مع الطباعة التلقائية
function simulateQuickSave(invoiceId, type) {
    testCounter++;
    const testId = `test_${testCounter}`;
    
    addTestResult(`🧪 اختبار ${testCounter}: محاكاة حفظ فاتورة ${type} رقم ${invoiceId}`, 'info');
    
    // محاكاة إعادة التوجيه مع معاملات الطباعة التلقائية
    const delay = parseInt(document.getElementById('testDelay').value);
    
    setTimeout(() => {
        addTestResult(`⏳ تأخير ${delay}ms انتهى، بدء الطباعة التلقائية...`, 'warning');
        
        // تشغيل الطباعة التلقائية
        const width = document.getElementById('testPrintWidth').value;
        const autoPrint = document.getElementById('testAutoPrint').value === '1';
        
        try {
            if (typeof quickPOSPrint === 'function') {
                quickPOSPrint(invoiceId, type);
                addTestResult(`✅ نجح: تم استدعاء quickPOSPrint للفاتورة ${invoiceId}`, 'success');
            } else {
                // الطريقة المباشرة
                const printUrl = `print_pos_invoice.php?id=${invoiceId}&type=${type}&width=${width}&auto_print=${autoPrint ? '1' : '0'}`;
                const printWindow = window.open(printUrl, 'TestPOSPrint', 'width=400,height=600,scrollbars=yes');
                
                if (printWindow) {
                    addTestResult(`✅ نجح: فتح نافذة الطباعة للفاتورة ${invoiceId}`, 'success');
                } else {
                    addTestResult(`❌ فشل: لم يتم فتح نافذة الطباعة (مانع النوافذ المنبثقة؟)`, 'danger');
                }
            }
        } catch (error) {
            addTestResult(`❌ خطأ: ${error.message}`, 'danger');
        }
    }, delay);
}

// اختبار الطباعة المباشرة
function testDirectPrint(invoiceId, type) {
    testCounter++;
    addTestResult(`🖨️ اختبار ${testCounter}: طباعة مباشرة للفاتورة ${type} رقم ${invoiceId}`, 'info');
    
    const width = document.getElementById('testPrintWidth').value;
    const autoPrint = document.getElementById('testAutoPrint').value === '1';
    
    try {
        if (typeof printPOSInvoice === 'function') {
            printPOSInvoice(invoiceId, type, {
                showPreview: false,
                autoPrint: autoPrint
            });
            addTestResult(`✅ نجح: تم استدعاء printPOSInvoice للفاتورة ${invoiceId}`, 'success');
        } else {
            const printUrl = `print_pos_invoice.php?id=${invoiceId}&type=${type}&width=${width}&auto_print=${autoPrint ? '1' : '0'}`;
            const printWindow = window.open(printUrl, 'DirectPOSPrint', 'width=400,height=600,scrollbars=yes');
            
            if (printWindow) {
                addTestResult(`✅ نجح: فتح نافذة الطباعة المباشرة للفاتورة ${invoiceId}`, 'success');
            } else {
                addTestResult(`❌ فشل: لم يتم فتح نافذة الطباعة (مانع النوافذ المنبثقة؟)`, 'danger');
            }
        }
    } catch (error) {
        addTestResult(`❌ خطأ: ${error.message}`, 'danger');
    }
}

// إضافة نتيجة اختبار
function addTestResult(message, type = 'info') {
    const resultsDiv = document.getElementById('testResults');
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    
    const alertClass = {
        'info': 'alert-info',
        'success': 'alert-success', 
        'warning': 'alert-warning',
        'danger': 'alert-danger'
    }[type] || 'alert-info';
    
    const resultHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <small class="text-muted">[${timestamp}]</small> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    if (resultsDiv.innerHTML.includes('لم يتم تشغيل أي اختبارات')) {
        resultsDiv.innerHTML = '';
    }
    
    resultsDiv.insertAdjacentHTML('afterbegin', resultHtml);
}

// مسح نتائج الاختبار
function clearTestResults() {
    document.getElementById('testResults').innerHTML = '<p class="text-muted">لم يتم تشغيل أي اختبارات بعد...</p>';
    testCounter = 0;
}

// رسالة ترحيب
document.addEventListener('DOMContentLoaded', function() {
    addTestResult('🚀 مرحباً بك في اختبار الطباعة التلقائية للفواتير السريعة!', 'info');
});
</script>

<?php require_once 'includes/footer.php'; ?>
