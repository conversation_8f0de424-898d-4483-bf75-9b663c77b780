<?php
/**
 * صفحة اختبار حفظ الإعدادات
 */

session_start();
require_once __DIR__ . '/config/unified_db_config.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من اتصال قاعدة البيانات
$db = getUnifiedDB();
if (!$db) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

echo "<h2>اختبار حفظ إعدادات الشركة</h2>";

// إنشاء جدول الإعدادات إذا لم يكن موجوداً
$create_table = "CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL UNIQUE,
    `setting_value` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($db->query($create_table)) {
    echo "<p style='color: green;'>✅ جدول system_settings جاهز</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الجدول: " . $db->error . "</p>";
}

// معالجة النموذج
if ($_POST['save_settings'] ?? false) {
    echo "<h3>محاولة حفظ الإعدادات...</h3>";
    
    $settings = [
        'company_name' => trim($_POST['company_name'] ?? 'شركة تجريبية'),
        'company_name_en' => trim($_POST['company_name_en'] ?? 'Test Company'),
        'company_address' => trim($_POST['company_address'] ?? 'عنوان تجريبي'),
        'company_phone' => trim($_POST['company_phone'] ?? '0501234567'),
        'company_email' => trim($_POST['company_email'] ?? '<EMAIL>'),
        'system_version' => trim($_POST['system_version'] ?? '2.0'),
        'show_logo_on_invoices' => isset($_POST['show_logo_on_invoices']) ? '1' : '0',
        'show_company_info_on_invoices' => isset($_POST['show_company_info_on_invoices']) ? '1' : '0'
    ];
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($settings as $key => $value) {
        echo "<p>محاولة حفظ: $key = $value</p>";
        
        $stmt = $db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        if (!$stmt) {
            echo "<p style='color: red;'>❌ خطأ في إعداد الاستعلام: " . $db->error . "</p>";
            $error_count++;
            continue;
        }
        
        $stmt->bind_param("ss", $key, $value);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم حفظ: $key</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>❌ فشل في حفظ $key: " . $stmt->error . "</p>";
            $error_count++;
        }
        $stmt->close();
    }
    
    echo "<hr>";
    echo "<p><strong>النتيجة:</strong> تم حفظ $success_count إعداد، فشل في $error_count إعداد</p>";
    
    if ($success_count > 0) {
        $_SESSION['success'] = "تم حفظ $success_count إعداد بنجاح!";
    }
    if ($error_count > 0) {
        $_SESSION['error'] = "فشل في حفظ $error_count إعداد";
    }
}

// عرض الرسائل
displayMessages();

// دالة لجلب إعداد
function getTestSetting($key, $default = '') {
    global $db;
    
    $stmt = $db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    if (!$stmt) {
        return $default;
    }
    
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row ? $row['setting_value'] : $default;
}
?>

<form method="POST">
    <h3>نموذج اختبار إعدادات الشركة:</h3>
    
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <td>اسم الشركة (عربي):</td>
            <td><input type="text" name="company_name" value="<?php echo htmlspecialchars(getTestSetting('company_name', 'شركة تجريبية')); ?>" style="width: 100%;"></td>
        </tr>
        <tr>
            <td>اسم الشركة (إنجليزي):</td>
            <td><input type="text" name="company_name_en" value="<?php echo htmlspecialchars(getTestSetting('company_name_en', 'Test Company')); ?>" style="width: 100%;"></td>
        </tr>
        <tr>
            <td>عنوان الشركة:</td>
            <td><textarea name="company_address" style="width: 100%;"><?php echo htmlspecialchars(getTestSetting('company_address', 'عنوان تجريبي')); ?></textarea></td>
        </tr>
        <tr>
            <td>رقم الهاتف:</td>
            <td><input type="text" name="company_phone" value="<?php echo htmlspecialchars(getTestSetting('company_phone', '0501234567')); ?>" style="width: 100%;"></td>
        </tr>
        <tr>
            <td>البريد الإلكتروني:</td>
            <td><input type="email" name="company_email" value="<?php echo htmlspecialchars(getTestSetting('company_email', '<EMAIL>')); ?>" style="width: 100%;"></td>
        </tr>
        <tr>
            <td>إصدار النظام:</td>
            <td><input type="text" name="system_version" value="<?php echo htmlspecialchars(getTestSetting('system_version', '2.0')); ?>" style="width: 100%;"></td>
        </tr>
        <tr>
            <td>عرض الشعار في الفواتير:</td>
            <td><input type="checkbox" name="show_logo_on_invoices" <?php echo getTestSetting('show_logo_on_invoices', '1') == '1' ? 'checked' : ''; ?>></td>
        </tr>
        <tr>
            <td>عرض معلومات الشركة في الفواتير:</td>
            <td><input type="checkbox" name="show_company_info_on_invoices" <?php echo getTestSetting('show_company_info_on_invoices', '1') == '1' ? 'checked' : ''; ?>></td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center; padding: 20px;">
                <button type="submit" name="save_settings" value="1" style="padding: 10px 20px; font-size: 16px;">حفظ الإعدادات</button>
            </td>
        </tr>
    </table>
</form>

<hr>

<h3>الإعدادات المحفوظة حالياً:</h3>
<?php
$all_settings = $db->query("SELECT * FROM system_settings ORDER BY setting_key");
if ($all_settings && $all_settings->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>المفتاح</th><th>القيمة</th><th>تاريخ التحديث</th></tr>";
    while ($row = $all_settings->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['setting_key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['setting_value']) . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد إعدادات محفوظة</p>";
}
?>

<hr>
<p>
    <a href="admin_system.php">العودة لصفحة الإعدادات الرئيسية</a> | 
    <a href="debug_settings.php">صفحة التشخيص</a> |
    <a href="test_invoice.php">اختبار الفاتورة</a>
</p>
