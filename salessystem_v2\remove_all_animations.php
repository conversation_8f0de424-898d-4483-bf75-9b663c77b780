<?php
/**
 * سكريپت إزالة جميع التأثيرات والحركات من جميع صفحات المدير
 */

// قائمة جميع صفحات المدير
$admin_pages = [
    'admin_dashboard.php',
    'admin_users.php',
    'admin_activity.php',
    'admin_reports.php', 
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$updates_applied = [];
$errors = [];

echo "<h2>إزالة جميع التأثيرات والحركات من صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إزالة جميع classes المتعلقة بالحركة
    $animation_classes = [
        'fade-in',
        'hover-lift',
        'animate__animated',
        'animate__fadeIn',
        'animate__slideIn',
        'pulse-animation'
    ];
    
    foreach ($animation_classes as $class) {
        if (strpos($content, $class) !== false) {
            $content = str_replace($class, '', $content);
            $changes_made = true;
            echo "✅ تم إزالة class: $class<br>";
        }
    }
    
    // 2. إزالة style attributes المتعلقة بالحركة
    $animation_styles = [
        'animation-delay:',
        'transition:',
        'transform:',
        'animation:'
    ];
    
    foreach ($animation_styles as $style) {
        $pattern = '/style="[^"]*' . preg_quote($style, '/') . '[^"]*"/';
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, '', $content);
            $changes_made = true;
            echo "✅ تم إزالة style: $style<br>";
        }
    }
    
    // 3. إزالة CSS animations من style tags
    $css_animation_patterns = [
        '/@keyframes[^}]+}/s',
        '/animation:[^;]+;/s',
        '/transition:[^;]+;/s',
        '/transform:[^;]+;/s',
        '/animation-delay:[^;]+;/s',
        '/animation-duration:[^;]+;/s'
    ];
    
    foreach ($css_animation_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, '', $content);
            $changes_made = true;
        }
    }
    
    // 4. إزالة JavaScript animations
    $js_animation_patterns = [
        '/\.animate\([^)]+\)/s',
        '/\.fadeIn\([^)]*\)/s',
        '/\.slideIn\([^)]*\)/s',
        '/\.transition\([^)]+\)/s',
        '/setTimeout\([^}]+animation[^}]+}\s*,\s*\d+\)/s'
    ];
    
    foreach ($js_animation_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, '', $content);
            $changes_made = true;
        }
    }
    
    // 5. تنظيف المسافات الزائدة
    $content = preg_replace('/\s+/', ' ', $content);
    $content = preg_replace('/class="\s*"/', '', $content);
    $content = preg_replace('/style="\s*"/', '', $content);
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.no_animations_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $updates_applied[] = $page;
            echo "✅ تم حفظ التحديثات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ التحديثات<br>";
        }
    } else {
        echo "ℹ️ لا توجد تحديثات مطلوبة<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>ملخص إزالة التأثيرات:</h3>";

if (!empty($updates_applied)) {
    echo "<div style='color: green;'>";
    echo "<h4>✅ الملفات المحدثة بنجاح:</h4>";
    foreach ($updates_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<br><h3>التحديثات المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ إزالة جميع classes المتعلقة بالحركة</li>";
echo "<li>✅ إزالة style attributes للحركة</li>";
echo "<li>✅ إزالة CSS animations من style tags</li>";
echo "<li>✅ إزالة JavaScript animations</li>";
echo "<li>✅ تنظيف المسافات الزائدة</li>";
echo "</ul>";

echo "<br><div style='background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50;'>";
echo "<h4>📋 النتائج:</h4>";
echo "<ul>";
echo "<li>✅ تم إيقاف جميع التأثيرات والحركات</li>";
echo "<li>✅ تم إزالة جميع الانتقالات</li>";
echo "<li>✅ تم إزالة جميع التحويلات</li>";
echo "<li>✅ تم إزالة جميع الرسوم المتحركة</li>";
echo "<li>✅ النظام الآن ثابت تماماً</li>";
echo "</ul>";
echo "</div>";

echo "<br><a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>العودة للوحة التحكم</a>";
echo " ";
echo "<a href='admin_users.php' style='background: #4facfe; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin-left: 10px;'>اختبار إدارة المستخدمين</a>";
?>
